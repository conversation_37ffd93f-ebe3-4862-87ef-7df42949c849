@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        direction: rtl;
        font-family: 'IBM Plex Sans Arabic', 'Tajawal', 'Cairo', 'Noto Sans Arabic', sans-serif;
    }

    body {
        @apply font-arabic text-secondary-800 bg-secondary-50;
    }

    /* RTL Support for common elements */
    .rtl {
        direction: rtl;
    }

    .ltr {
        direction: ltr;
    }
}

@layer components {
    /* Custom button styles */
    .btn-primary {
        @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out;
    }

    .btn-secondary {
        @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out;
    }

    /* Custom input styles */
    .input-field {
        @apply block w-full rounded-lg border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-right;
    }

    /* Card styles */
    .card {
        @apply bg-white rounded-lg shadow-sm border border-secondary-200 p-6;
    }

    /* Arabic text alignment */
    .text-arabic {
        text-align: right;
        direction: rtl;
    }

    /* Logo container */
    .logo-container {
        @apply flex items-center justify-center;
    }
}
