@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        direction: rtl;
        font-family: 'IBM Plex Sans Arabic', 'Tajawal', 'Cairo', 'Noto Sans Arabic', sans-serif;
    }

    body {
        @apply font-arabic text-secondary-800 bg-secondary-50;
    }

    /* RTL Support for common elements */
    .rtl {
        direction: rtl;
    }

    .ltr {
        direction: ltr;
    }
}

@layer components {
    /* Custom button styles */
    .btn-primary {
        @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out;
    }

    .btn-secondary {
        @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out;
    }

    /* Custom input styles */
    .input-field {
        @apply block w-full rounded-lg border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-right;
    }

    /* Card styles */
    .card {
        @apply bg-white rounded-lg shadow-sm border border-secondary-200 p-6;
    }

    /* Arabic text alignment */
    .text-arabic {
        text-align: right;
        direction: rtl;
    }

    /* Logo container */
    .logo-container {
        @apply flex items-center justify-center;
    }

    /* Navigation styles */
    .nav-item-active {
        @apply bg-primary-50 border-primary-500 text-primary-700;
        border-right-width: 4px;
    }

    .nav-item-inactive {
        @apply text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900;
    }

    /* Sidebar scrollbar */
    .sidebar-scroll::-webkit-scrollbar {
        width: 4px;
    }

    .sidebar-scroll::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    .sidebar-scroll::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 2px;
    }

    .sidebar-scroll::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Mobile menu animation */
    .mobile-menu-enter {
        transform: translateX(100%);
    }

    .mobile-menu-enter-active {
        transform: translateX(0);
        transition: transform 300ms ease-in-out;
    }

    .mobile-menu-exit {
        transform: translateX(0);
    }

    .mobile-menu-exit-active {
        transform: translateX(100%);
        transition: transform 300ms ease-in-out;
    }
}
