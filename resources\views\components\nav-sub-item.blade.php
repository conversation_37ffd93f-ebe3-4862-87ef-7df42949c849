@props(['href', 'active' => false])

@php
$classes = $active 
    ? 'bg-primary-50 text-primary-700 group flex items-center px-3 py-2 text-sm font-medium rounded-lg border-r-2 border-primary-500'
    : 'text-secondary-500 hover:bg-secondary-50 hover:text-secondary-700 group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200';
@endphp

<a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }} wire:navigate>
    <span class="w-2 h-2 bg-current rounded-full ml-3 flex-shrink-0 opacity-50"></span>
    <span class="font-arabic">{{ $slot }}</span>
</a>
