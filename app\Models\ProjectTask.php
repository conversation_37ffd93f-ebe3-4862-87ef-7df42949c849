<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ProjectTask extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'project_id',
        'parent_task_id',
        'title_ar',
        'title_en',
        'description_ar',
        'description_en',
        'type',
        'status',
        'priority',
        'progress_percentage',
        'start_date',
        'due_date',
        'completed_date',
        'estimated_hours',
        'actual_hours',
        'assigned_to',
        'created_by',
        'watchers',
        'dependencies',
        'tags',
        'sort_order',
    ];

    protected $casts = [
        'start_date' => 'date',
        'due_date' => 'date',
        'completed_date' => 'date',
        'estimated_hours' => 'integer',
        'actual_hours' => 'integer',
        'progress_percentage' => 'integer',
        'watchers' => 'array',
        'dependencies' => 'array',
        'tags' => 'array',
        'sort_order' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the project that owns the task.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the parent task.
     */
    public function parentTask(): BelongsTo
    {
        return $this->belongsTo(ProjectTask::class, 'parent_task_id');
    }

    /**
     * Get the child tasks.
     */
    public function childTasks(): HasMany
    {
        return $this->hasMany(ProjectTask::class, 'parent_task_id');
    }

    /**
     * Get the user assigned to this task.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who created the task.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the time logs for this task.
     */
    public function timeLogs(): HasMany
    {
        return $this->hasMany(ProjectTimeLog::class, 'task_id');
    }

    /**
     * Get the files for this task.
     */
    public function files(): HasMany
    {
        return $this->hasMany(ProjectFile::class, 'task_id');
    }

    /**
     * Scope to get tasks by status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get completed tasks.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get pending tasks.
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['todo', 'in_progress']);
    }

    /**
     * Scope to get overdue tasks.
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope to filter by priority.
     */
    public function scopePriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get tasks assigned to a user.
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Get the task's display title.
     */
    public function getDisplayTitleAttribute(): string
    {
        return $this->title_ar ?? $this->title_en ?? 'مهمة بدون عنوان';
    }

    /**
     * Check if task is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get days until due date.
     */
    public function getDaysUntilDue(): int
    {
        if (!$this->due_date) {
            return 0;
        }
        
        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get days overdue.
     */
    public function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }
        
        return $this->due_date->diffInDays(now());
    }

    /**
     * Check if task is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if task is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Get task duration in days.
     */
    public function getDurationInDays(): int
    {
        if (!$this->start_date || !$this->due_date) {
            return 0;
        }
        
        return $this->start_date->diffInDays($this->due_date);
    }

    /**
     * Get actual duration in days.
     */
    public function getActualDurationInDays(): int
    {
        if (!$this->start_date) {
            return 0;
        }
        
        $endDate = $this->completed_date ?? now();
        return $this->start_date->diffInDays($endDate);
    }

    /**
     * Get time utilization percentage.
     */
    public function getTimeUtilizationAttribute(): float
    {
        if ($this->estimated_hours <= 0) {
            return 0;
        }
        
        return ($this->actual_hours / $this->estimated_hours) * 100;
    }

    /**
     * Get remaining hours.
     */
    public function getRemainingHoursAttribute(): int
    {
        return max(0, $this->estimated_hours - $this->actual_hours);
    }

    /**
     * Mark task as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'progress_percentage' => 100,
            'completed_date' => now(),
        ]);
    }

    /**
     * Mark task as in progress.
     */
    public function markAsInProgress(): void
    {
        $this->update([
            'status' => 'in_progress',
        ]);
    }

    /**
     * Update progress percentage.
     */
    public function updateProgress(int $percentage): void
    {
        $percentage = max(0, min(100, $percentage));
        
        $status = match(true) {
            $percentage === 0 => 'todo',
            $percentage === 100 => 'completed',
            default => 'in_progress'
        };

        $this->update([
            'progress_percentage' => $percentage,
            'status' => $status,
            'completed_date' => $percentage === 100 ? now() : null,
        ]);
    }

    /**
     * Add time log to task.
     */
    public function addTimeLog(int $minutes, string $description, bool $billable = true): ProjectTimeLog
    {
        return $this->timeLogs()->create([
            'project_id' => $this->project_id,
            'user_id' => auth()->id(),
            'description_ar' => $description,
            'start_time' => now(),
            'end_time' => now()->addMinutes($minutes),
            'duration_minutes' => $minutes,
            'is_billable' => $billable,
        ]);
    }

    /**
     * Get total logged time in minutes.
     */
    public function getTotalLoggedTimeAttribute(): int
    {
        return $this->timeLogs()->sum('duration_minutes');
    }

    /**
     * Get billable time in minutes.
     */
    public function getBillableTimeAttribute(): int
    {
        return $this->timeLogs()->where('is_billable', true)->sum('duration_minutes');
    }
}
