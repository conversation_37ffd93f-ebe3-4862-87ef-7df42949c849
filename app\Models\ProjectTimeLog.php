<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectTimeLog extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'project_id',
        'task_id',
        'user_id',
        'description_ar',
        'description_en',
        'start_time',
        'end_time',
        'duration_minutes',
        'is_billable',
        'hourly_rate',
        'total_cost',
        'status',
        'approved_by',
        'approved_at',
        'notes_ar',
        'notes_en',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'duration_minutes' => 'integer',
        'is_billable' => 'boolean',
        'hourly_rate' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the project that owns the time log.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the task associated with the time log.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(ProjectTask::class, 'task_id');
    }

    /**
     * Get the user who logged the time.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who approved the time log.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the time log's display description.
     */
    public function getDisplayDescriptionAttribute(): string
    {
        return $this->description_ar ?? $this->description_en ?? 'وقت مسجل';
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * Calculate total cost.
     */
    public function calculateCost(): void
    {
        if ($this->is_billable && $this->hourly_rate > 0) {
            $hours = $this->duration_minutes / 60;
            $this->update(['total_cost' => $hours * $this->hourly_rate]);
        }
    }
}
