<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Technical Services
            'view_technical_services',
            'create_technical_services',
            'edit_technical_services',
            'delete_technical_services',

            // Clients
            'view_clients',
            'create_clients',
            'edit_clients',
            'delete_clients',

            // Finance
            'view_finance',
            'create_invoices',
            'edit_invoices',
            'delete_invoices',
            'view_payments',
            'create_payments',
            'edit_payments',
            'delete_payments',
            'view_expenses',
            'create_expenses',
            'edit_expenses',
            'delete_expenses',
            'view_financial_reports',

            // Projects
            'view_projects',
            'create_projects',
            'edit_projects',
            'delete_projects',
            'manage_project_tasks',
            'view_project_files',
            'upload_project_files',
            'delete_project_files',

            // Team Management
            'view_team',
            'create_team_members',
            'edit_team_members',
            'delete_team_members',
            'view_team_performance',
            'manage_team_attendance',
            'manage_team_salaries',

            // System Administration
            'view_system_settings',
            'edit_system_settings',
            'manage_user_roles',
            'view_system_logs',
            'backup_system',
            'restore_system',

            // Dashboard and Reports
            'view_dashboard',
            'view_analytics',
            'export_data',
            'import_data',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Founder role - has all permissions
        $founderRole = Role::firstOrCreate(['name' => 'founder']);
        $founderRole->syncPermissions(Permission::all());

        // Admin role - has most permissions except system critical ones
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminPermissions = Permission::whereNotIn('name', [
            'backup_system',
            'restore_system',
            'manage_user_roles'
        ])->get();
        $adminRole->syncPermissions($adminPermissions);

        // Manager role - department specific permissions
        $managerRole = Role::firstOrCreate(['name' => 'manager']);
        $managerPermissions = [
            'view_dashboard',
            'view_analytics',
            'view_technical_services',
            'view_clients',
            'view_projects',
            'edit_projects',
            'manage_project_tasks',
            'view_team',
            'view_team_performance',
            'export_data',
        ];
        $managerRole->syncPermissions($managerPermissions);

        // Employee role - basic permissions
        $employeeRole = Role::firstOrCreate(['name' => 'employee']);
        $employeePermissions = [
            'view_dashboard',
            'view_technical_services',
            'view_clients',
            'view_projects',
            'manage_project_tasks',
            'view_project_files',
            'upload_project_files',
        ];
        $employeeRole->syncPermissions($employeePermissions);

        // Client role - limited view permissions
        $clientRole = Role::firstOrCreate(['name' => 'client']);
        $clientPermissions = [
            'view_projects',
            'view_project_files',
        ];
        $clientRole->syncPermissions($clientPermissions);

        // Create default founder user
        $founder = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مؤسس النظام',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        $founder->assignRole('founder');

        // Create sample admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        $admin->assignRole('admin');
    }
}
