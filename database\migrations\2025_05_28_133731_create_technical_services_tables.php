<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Service Categories Table
        Schema::create('service_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar')->comment('Arabic name');
            $table->string('name_en')->nullable()->comment('English name');
            $table->text('description_ar')->nullable()->comment('Arabic description');
            $table->text('description_en')->nullable()->comment('English description');
            $table->string('icon')->nullable()->comment('Category icon');
            $table->string('color', 7)->default('#3b82f6')->comment('Category color');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->foreignId('parent_id')->nullable()->constrained('service_categories')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['is_active', 'sort_order']);
            $table->index('parent_id');
        });

        // Technical Services Table
        Schema::create('technical_services', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Public identifier');
            $table->string('name_ar')->comment('Arabic service name');
            $table->string('name_en')->nullable()->comment('English service name');
            $table->text('description_ar')->comment('Arabic description');
            $table->text('description_en')->nullable()->comment('English description');
            $table->text('features_ar')->nullable()->comment('Arabic features list');
            $table->text('features_en')->nullable()->comment('English features list');
            $table->foreignId('category_id')->constrained('service_categories')->onDelete('restrict');
            $table->decimal('base_price', 10, 2)->default(0)->comment('Base price in SAR');
            $table->integer('estimated_hours')->nullable()->comment('Estimated completion hours');
            $table->integer('estimated_days')->nullable()->comment('Estimated completion days');
            $table->enum('complexity_level', ['simple', 'medium', 'complex', 'enterprise'])->default('medium');
            $table->enum('status', ['draft', 'active', 'inactive', 'discontinued'])->default('draft');
            $table->json('requirements')->nullable()->comment('Service requirements');
            $table->json('deliverables')->nullable()->comment('Service deliverables');
            $table->json('technologies')->nullable()->comment('Technologies used');
            $table->string('image')->nullable()->comment('Service image');
            $table->json('gallery')->nullable()->comment('Service gallery images');
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_customizable')->default(true);
            $table->integer('sort_order')->default(0);
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status', 'is_featured']);
            $table->index(['category_id', 'status']);
            $table->index('complexity_level');
            $table->fullText(['name_ar', 'description_ar']);
        });

        // Service Pricing Tiers Table
        Schema::create('service_pricing_tiers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained('technical_services')->onDelete('cascade');
            $table->string('name_ar')->comment('Arabic tier name');
            $table->string('name_en')->nullable()->comment('English tier name');
            $table->text('description_ar')->nullable()->comment('Arabic description');
            $table->text('description_en')->nullable()->comment('English description');
            $table->decimal('price', 10, 2)->comment('Tier price in SAR');
            $table->integer('estimated_hours')->nullable();
            $table->integer('estimated_days')->nullable();
            $table->json('features')->nullable()->comment('Tier-specific features');
            $table->json('limitations')->nullable()->comment('Tier limitations');
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->index(['service_id', 'is_active']);
            $table->index('is_popular');
        });

        // Service Templates Table
        Schema::create('service_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained('technical_services')->onDelete('cascade');
            $table->string('name_ar')->comment('Arabic template name');
            $table->string('name_en')->nullable()->comment('English template name');
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->json('template_data')->comment('Template configuration');
            $table->json('default_settings')->nullable()->comment('Default settings');
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->index(['service_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_templates');
        Schema::dropIfExists('service_pricing_tiers');
        Schema::dropIfExists('technical_services');
        Schema::dropIfExists('service_categories');
    }
};
