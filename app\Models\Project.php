<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Project extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'project_code',
        'client_id',
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'start_date',
        'end_date',
        'actual_start_date',
        'actual_end_date',
        'estimated_hours',
        'actual_hours',
        'budget',
        'actual_cost',
        'hourly_rate',
        'currency',
        'status',
        'priority',
        'progress_percentage',
        'project_type',
        'category',
        'complexity_level',
        'project_manager_id',
        'team_members',
        'technologies',
        'requirements',
        'deliverables',
        'milestones',
        'client_approval_status',
        'client_approved_at',
        'client_feedback',
        'approval_workflow',
        'notes_ar',
        'notes_en',
        'custom_fields',
        'tags',
        'is_template',
        'template_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'actual_start_date' => 'date',
        'actual_end_date' => 'date',
        'estimated_hours' => 'integer',
        'actual_hours' => 'integer',
        'budget' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'progress_percentage' => 'integer',
        'team_members' => 'array',
        'technologies' => 'array',
        'requirements' => 'array',
        'deliverables' => 'array',
        'milestones' => 'array',
        'client_approved_at' => 'datetime',
        'approval_workflow' => 'array',
        'custom_fields' => 'array',
        'tags' => 'array',
        'is_template' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
            if (empty($model->project_code)) {
                $model->project_code = 'PRJ-' . str_pad(static::count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }

    /**
     * Get the client that owns the project.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the project manager.
     */
    public function projectManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'project_manager_id');
    }

    /**
     * Get the user who created the project.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the project.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the template project.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Project::class, 'template_id');
    }

    /**
     * Get projects created from this template.
     */
    public function templatedProjects(): HasMany
    {
        return $this->hasMany(Project::class, 'template_id');
    }

    /**
     * Get the tasks for this project.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(ProjectTask::class);
    }

    /**
     * Get the files for this project.
     */
    public function files(): HasMany
    {
        return $this->hasMany(ProjectFile::class);
    }

    /**
     * Get the time logs for this project.
     */
    public function timeLogs(): HasMany
    {
        return $this->hasMany(ProjectTimeLog::class);
    }

    /**
     * Get the invoices for this project.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Scope to get only active projects.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get completed projects.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by priority.
     */
    public function scopePriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Get the project's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name_ar ?? $this->name_en ?? 'مشروع بدون اسم';
    }

    /**
     * Get the project's completion percentage.
     */
    public function getCompletionPercentageAttribute(): int
    {
        return $this->progress_percentage;
    }

    /**
     * Check if project is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->end_date && $this->end_date->isPast() && $this->status !== 'completed';
    }

    /**
     * Get project duration in days.
     */
    public function getDurationInDays(): int
    {
        if (!$this->start_date || !$this->end_date) {
            return 0;
        }
        
        return $this->start_date->diffInDays($this->end_date);
    }

    /**
     * Get actual duration in days.
     */
    public function getActualDurationInDays(): int
    {
        if (!$this->actual_start_date) {
            return 0;
        }
        
        $endDate = $this->actual_end_date ?? now();
        return $this->actual_start_date->diffInDays($endDate);
    }

    /**
     * Get budget utilization percentage.
     */
    public function getBudgetUtilizationAttribute(): float
    {
        if ($this->budget <= 0) {
            return 0;
        }
        
        return ($this->actual_cost / $this->budget) * 100;
    }

    /**
     * Get remaining budget.
     */
    public function getRemainingBudgetAttribute(): float
    {
        return $this->budget - $this->actual_cost;
    }

    /**
     * Check if project is on budget.
     */
    public function isOnBudget(): bool
    {
        return $this->actual_cost <= $this->budget;
    }

    /**
     * Get team member count.
     */
    public function getTeamMemberCountAttribute(): int
    {
        return is_array($this->team_members) ? count($this->team_members) : 0;
    }

    /**
     * Get completed tasks count.
     */
    public function getCompletedTasksCountAttribute(): int
    {
        return $this->tasks()->where('status', 'completed')->count();
    }

    /**
     * Get total tasks count.
     */
    public function getTotalTasksCountAttribute(): int
    {
        return $this->tasks()->count();
    }

    /**
     * Get tasks completion percentage.
     */
    public function getTasksCompletionPercentageAttribute(): float
    {
        $totalTasks = $this->total_tasks_count;
        if ($totalTasks === 0) {
            return 0;
        }
        
        return ($this->completed_tasks_count / $totalTasks) * 100;
    }
}
