<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Departments Table
        Schema::create('departments', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('Department code');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->string('color', 7)->default('#3b82f6');
            $table->string('icon')->nullable();
            $table->foreignId('manager_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('parent_id')->nullable()->constrained('departments')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['is_active', 'sort_order']);
            $table->index('manager_id');
            $table->index('parent_id');
        });

        // Team Members Table (extends users table)
        Schema::create('team_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('employee_id')->unique()->comment('Employee ID');
            $table->foreignId('department_id')->constrained('departments')->onDelete('restrict');
            $table->string('position_ar');
            $table->string('position_en')->nullable();
            $table->enum('employment_type', ['full_time', 'part_time', 'contract', 'intern', 'freelancer'])->default('full_time');
            $table->enum('status', ['active', 'inactive', 'on_leave', 'terminated'])->default('active');
            
            // Personal Information
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('nationality')->nullable();
            $table->string('id_number')->nullable();
            $table->string('passport_number')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            
            // Address Information
            $table->string('country')->default('Saudi Arabia');
            $table->string('city')->nullable();
            $table->string('district')->nullable();
            $table->text('address')->nullable();
            $table->string('postal_code')->nullable();
            
            // Employment Details
            $table->date('hire_date');
            $table->date('probation_end_date')->nullable();
            $table->date('contract_end_date')->nullable();
            $table->decimal('basic_salary', 10, 2)->default(0);
            $table->string('currency', 3)->default('SAR');
            $table->json('benefits')->nullable();
            $table->json('skills')->nullable();
            $table->json('certifications')->nullable();
            $table->text('notes')->nullable();
            
            // Reporting Structure
            $table->foreignId('direct_manager_id')->nullable()->constrained('users')->onDelete('set null');
            $table->json('reports_to')->nullable()->comment('Multiple reporting relationships');
            
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['department_id', 'status']);
            $table->index(['employment_type', 'status']);
            $table->index('direct_manager_id');
            $table->index('hire_date');
        });

        // Attendance Table
        Schema::create('attendance', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_member_id')->constrained('team_members')->onDelete('cascade');
            $table->date('date');
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
            $table->time('break_start_time')->nullable();
            $table->time('break_end_time')->nullable();
            $table->integer('total_hours_minutes')->default(0)->comment('Total working minutes');
            $table->integer('break_minutes')->default(0);
            $table->integer('overtime_minutes')->default(0);
            $table->enum('status', ['present', 'absent', 'late', 'half_day', 'sick_leave', 'vacation', 'official_leave'])->default('present');
            $table->text('notes')->nullable();
            $table->string('location')->nullable()->comment('Check-in location');
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->boolean('is_remote')->default(false);
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            
            $table->unique(['team_member_id', 'date']);
            $table->index(['date', 'status']);
            $table->index('team_member_id');
        });

        // Leave Requests Table
        Schema::create('leave_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_member_id')->constrained('team_members')->onDelete('cascade');
            $table->enum('type', ['annual', 'sick', 'maternity', 'paternity', 'emergency', 'unpaid', 'other'])->default('annual');
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('total_days');
            $table->text('reason_ar');
            $table->text('reason_en')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'cancelled'])->default('pending');
            $table->text('manager_notes')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->json('attachments')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['team_member_id', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index('type');
        });

        // Performance Reviews Table
        Schema::create('performance_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_member_id')->constrained('team_members')->onDelete('cascade');
            $table->foreignId('reviewer_id')->constrained('users')->onDelete('restrict');
            $table->enum('type', ['annual', 'quarterly', 'monthly', 'probation', 'project_based'])->default('annual');
            $table->date('review_period_start');
            $table->date('review_period_end');
            $table->date('review_date');
            
            // Performance Metrics
            $table->integer('overall_rating')->nullable()->comment('1-10 scale');
            $table->json('performance_metrics')->nullable()->comment('Various KPIs and ratings');
            $table->text('achievements_ar')->nullable();
            $table->text('achievements_en')->nullable();
            $table->text('areas_for_improvement_ar')->nullable();
            $table->text('areas_for_improvement_en')->nullable();
            $table->text('goals_next_period_ar')->nullable();
            $table->text('goals_next_period_en')->nullable();
            $table->text('employee_comments_ar')->nullable();
            $table->text('employee_comments_en')->nullable();
            $table->text('manager_comments_ar')->nullable();
            $table->text('manager_comments_en')->nullable();
            
            // Development Plan
            $table->json('training_recommendations')->nullable();
            $table->json('career_development_plan')->nullable();
            $table->decimal('salary_recommendation', 10, 2)->nullable();
            $table->text('promotion_recommendation')->nullable();
            
            $table->enum('status', ['draft', 'pending_employee_review', 'pending_manager_approval', 'completed'])->default('draft');
            $table->timestamp('employee_acknowledged_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['team_member_id', 'review_date']);
            $table->index(['reviewer_id', 'status']);
            $table->index('type');
        });

        // Salary History Table
        Schema::create('salary_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_member_id')->constrained('team_members')->onDelete('cascade');
            $table->decimal('basic_salary', 10, 2);
            $table->json('allowances')->nullable()->comment('Housing, transport, etc.');
            $table->json('deductions')->nullable()->comment('Insurance, taxes, etc.');
            $table->decimal('gross_salary', 10, 2);
            $table->decimal('net_salary', 10, 2);
            $table->string('currency', 3)->default('SAR');
            $table->date('effective_date');
            $table->date('end_date')->nullable();
            $table->enum('change_reason', ['promotion', 'annual_increase', 'performance', 'market_adjustment', 'demotion', 'other'])->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('approved_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            
            $table->index(['team_member_id', 'effective_date']);
            $table->index('effective_date');
        });

        // Training Records Table
        Schema::create('training_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_member_id')->constrained('team_members')->onDelete('cascade');
            $table->string('training_name_ar');
            $table->string('training_name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->enum('type', ['internal', 'external', 'online', 'certification', 'workshop', 'conference'])->default('internal');
            $table->string('provider')->nullable();
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->integer('duration_hours')->nullable();
            $table->decimal('cost', 8, 2)->default(0);
            $table->enum('status', ['planned', 'in_progress', 'completed', 'cancelled'])->default('planned');
            $table->text('outcome_ar')->nullable();
            $table->text('outcome_en')->nullable();
            $table->string('certificate_path')->nullable();
            $table->date('certificate_expiry')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['team_member_id', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('training_records');
        Schema::dropIfExists('salary_history');
        Schema::dropIfExists('performance_reviews');
        Schema::dropIfExists('leave_requests');
        Schema::dropIfExists('attendance');
        Schema::dropIfExists('team_members');
        Schema::dropIfExists('departments');
    }
};
