<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Projects Table
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Public identifier');
            $table->string('project_code')->unique()->comment('Internal project code');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar');
            $table->text('description_en')->nullable();
            $table->foreignId('client_id')->constrained('clients')->onDelete('restrict');
            $table->foreignId('service_id')->nullable()->constrained('technical_services')->onDelete('set null');
            
            // Project Timeline
            $table->date('start_date');
            $table->date('end_date');
            $table->date('actual_start_date')->nullable();
            $table->date('actual_end_date')->nullable();
            $table->integer('estimated_hours')->nullable();
            $table->integer('actual_hours')->default(0);
            
            // Financial Information
            $table->decimal('budget', 12, 2)->default(0);
            $table->decimal('actual_cost', 12, 2)->default(0);
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->string('currency', 3)->default('SAR');
            
            // Project Status and Priority
            $table->enum('status', ['planning', 'active', 'on_hold', 'completed', 'cancelled', 'archived'])->default('planning');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->integer('progress_percentage')->default(0);
            
            // Technical Details (as specified in requirements)
            $table->text('host_url')->nullable()->comment('Hosting URL with credentials');
            $table->text('host_credentials')->nullable()->comment('Encrypted hosting credentials');
            $table->text('domain_url')->nullable()->comment('Domain URL with credentials');
            $table->text('domain_credentials')->nullable()->comment('Encrypted domain credentials');
            $table->text('email_provider_url')->nullable()->comment('Email provider URL');
            $table->json('email_accounts')->nullable()->comment('Email accounts with encrypted credentials');
            $table->string('whatsapp_group_link')->nullable()->comment('WhatsApp group for project communication');
            $table->json('whatsapp_details')->nullable()->comment('WhatsApp group details');
            
            // Project Management
            $table->foreignId('project_manager_id')->nullable()->constrained('users')->onDelete('set null');
            $table->json('team_members')->nullable()->comment('Assigned team members with roles');
            $table->json('technologies')->nullable()->comment('Technologies and tools used');
            $table->json('requirements')->nullable()->comment('Project requirements');
            $table->json('deliverables')->nullable()->comment('Project deliverables');
            $table->json('milestones')->nullable()->comment('Project milestones');
            
            // Client Approval and Communication
            $table->enum('client_approval_status', ['pending', 'approved', 'rejected', 'changes_requested'])->default('pending');
            $table->timestamp('client_approved_at')->nullable();
            $table->text('client_feedback')->nullable();
            $table->json('approval_workflow')->nullable();
            
            // Additional Information
            $table->text('notes_ar')->nullable();
            $table->text('notes_en')->nullable();
            $table->json('custom_fields')->nullable();
            $table->json('tags')->nullable();
            $table->boolean('is_template')->default(false);
            $table->foreignId('template_id')->nullable()->constrained('projects')->onDelete('set null');
            
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['client_id', 'status']);
            $table->index(['status', 'priority']);
            $table->index(['start_date', 'end_date']);
            $table->index('project_manager_id');
            $table->index('client_approval_status');
            $table->fullText(['name_ar', 'description_ar']);
        });

        // Project Tasks Table
        Schema::create('project_tasks', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Public identifier');
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('parent_task_id')->nullable()->constrained('project_tasks')->onDelete('cascade');
            $table->string('title_ar');
            $table->string('title_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            
            // Task Details
            $table->enum('type', ['task', 'milestone', 'phase', 'bug', 'feature', 'review'])->default('task');
            $table->enum('status', ['todo', 'in_progress', 'review', 'testing', 'completed', 'cancelled'])->default('todo');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->integer('progress_percentage')->default(0);
            
            // Timeline
            $table->date('start_date')->nullable();
            $table->date('due_date')->nullable();
            $table->date('completed_date')->nullable();
            $table->integer('estimated_hours')->nullable();
            $table->integer('actual_hours')->default(0);
            
            // Assignment
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->json('watchers')->nullable()->comment('Users watching this task');
            
            // Dependencies
            $table->json('dependencies')->nullable()->comment('Task dependencies');
            $table->json('tags')->nullable();
            $table->integer('sort_order')->default(0);
            
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['project_id', 'status']);
            $table->index(['assigned_to', 'status']);
            $table->index(['due_date', 'status']);
            $table->index('parent_task_id');
            $table->index('type');
        });

        // Project Files Table
        Schema::create('project_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('task_id')->nullable()->constrained('project_tasks')->onDelete('set null');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->enum('type', ['document', 'image', 'video', 'audio', 'archive', 'code', 'design', 'other'])->default('document');
            $table->string('file_path');
            $table->string('file_name');
            $table->string('file_type');
            $table->integer('file_size');
            $table->string('version', 10)->default('1.0');
            $table->boolean('is_final')->default(false);
            $table->boolean('is_confidential')->default(false);
            $table->json('access_permissions')->nullable();
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['project_id', 'type']);
            $table->index('task_id');
            $table->index('is_confidential');
        });

        // Project Time Tracking Table
        Schema::create('project_time_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('task_id')->nullable()->constrained('project_tasks')->onDelete('set null');
            $table->foreignId('user_id')->constrained('users')->onDelete('restrict');
            $table->text('description_ar');
            $table->text('description_en')->nullable();
            $table->datetime('start_time');
            $table->datetime('end_time')->nullable();
            $table->integer('duration_minutes')->default(0);
            $table->boolean('is_billable')->default(true);
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->decimal('total_cost', 10, 2)->default(0);
            $table->enum('status', ['active', 'paused', 'completed'])->default('completed');
            $table->timestamps();
            
            $table->index(['project_id', 'user_id']);
            $table->index(['task_id', 'user_id']);
            $table->index(['start_time', 'end_time']);
            $table->index('is_billable');
        });

        // Project Milestones Table
        Schema::create('project_milestones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->date('due_date');
            $table->date('completed_date')->nullable();
            $table->enum('status', ['pending', 'in_progress', 'completed', 'overdue'])->default('pending');
            $table->integer('progress_percentage')->default(0);
            $table->json('deliverables')->nullable();
            $table->boolean('is_critical')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['project_id', 'status']);
            $table->index(['due_date', 'status']);
            $table->index('is_critical');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_milestones');
        Schema::dropIfExists('project_time_logs');
        Schema::dropIfExists('project_files');
        Schema::dropIfExists('project_tasks');
        Schema::dropIfExists('projects');
    }
};
