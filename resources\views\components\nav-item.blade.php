@props(['href', 'active' => false, 'icon' => null])

@php
$classes = $active 
    ? 'bg-primary-50 border-primary-500 text-primary-700 group flex items-center px-3 py-2 text-sm font-medium border-r-4 rounded-l-lg'
    : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900 group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200';
@endphp

<a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }} wire:navigate>
    @if($icon)
        <x-icon name="{{ $icon }}" class="ml-3 flex-shrink-0 h-5 w-5 {{ $active ? 'text-primary-500' : 'text-secondary-400 group-hover:text-secondary-500' }}" />
    @endif
    <span class="font-arabic">{{ $slot }}</span>
</a>
