<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'service_id',
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'template_data',
        'default_settings',
        'is_active',
        'usage_count',
    ];

    protected $casts = [
        'template_data' => 'array',
        'default_settings' => 'array',
        'is_active' => 'boolean',
        'usage_count' => 'integer',
    ];

    /**
     * Get the service that owns the template.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(TechnicalService::class, 'service_id');
    }

    /**
     * Scope to get only active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the display name based on current locale.
     */
    public function getDisplayNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    /**
     * Get the display description based on current locale.
     */
    public function getDisplayDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : ($this->description_en ?? $this->description_ar);
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }
}
