<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Activity extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'action',
        'description_ar',
        'description_en',
        'subject_type',
        'subject_id',
        'properties',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'properties' => 'array',
    ];

    /**
     * Get the user that performed the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subject of the activity.
     */
    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get recent activities.
     */
    public function scopeRecent($query, int $limit = 10)
    {
        return $query->latest()->limit($limit);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by action.
     */
    public function scopeAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get the activity's display description.
     */
    public function getDisplayDescriptionAttribute(): string
    {
        return $this->description_ar ?? $this->description_en ?? 'نشاط بدون وصف';
    }

    /**
     * Get the activity icon based on type.
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            'client' => match($this->action) {
                'created' => 'user-plus',
                'updated' => 'user-edit',
                'deleted' => 'user-minus',
                default => 'user'
            },
            'project' => match($this->action) {
                'created' => 'folder-plus',
                'updated' => 'folder-edit',
                'completed' => 'folder-check',
                'deleted' => 'folder-minus',
                default => 'folder'
            },
            'task' => match($this->action) {
                'created' => 'plus-circle',
                'updated' => 'edit',
                'completed' => 'check-circle',
                'deleted' => 'minus-circle',
                default => 'circle'
            },
            'invoice' => match($this->action) {
                'created' => 'file-plus',
                'sent' => 'send',
                'paid' => 'dollar-sign',
                'deleted' => 'file-minus',
                default => 'file-text'
            },
            'payment' => match($this->action) {
                'received' => 'credit-card',
                'refunded' => 'rotate-ccw',
                default => 'dollar-sign'
            },
            'user' => match($this->action) {
                'login' => 'log-in',
                'logout' => 'log-out',
                'created' => 'user-plus',
                'updated' => 'user-edit',
                default => 'user'
            },
            default => 'activity'
        };
    }

    /**
     * Get the activity color based on type and action.
     */
    public function getColorAttribute(): string
    {
        return match($this->type) {
            'client' => match($this->action) {
                'created' => 'green',
                'updated' => 'blue',
                'deleted' => 'red',
                default => 'blue'
            },
            'project' => match($this->action) {
                'created' => 'green',
                'updated' => 'blue',
                'completed' => 'green',
                'deleted' => 'red',
                default => 'blue'
            },
            'task' => match($this->action) {
                'created' => 'green',
                'updated' => 'blue',
                'completed' => 'green',
                'deleted' => 'red',
                default => 'blue'
            },
            'invoice' => match($this->action) {
                'created' => 'green',
                'sent' => 'blue',
                'paid' => 'green',
                'overdue' => 'red',
                'deleted' => 'red',
                default => 'yellow'
            },
            'payment' => match($this->action) {
                'received' => 'green',
                'refunded' => 'red',
                default => 'green'
            },
            'user' => match($this->action) {
                'login' => 'green',
                'logout' => 'gray',
                'created' => 'green',
                'updated' => 'blue',
                default => 'blue'
            },
            default => 'gray'
        };
    }

    /**
     * Log an activity.
     */
    public static function log(
        string $type,
        string $action,
        string $descriptionAr,
        ?string $descriptionEn = null,
        ?Model $subject = null,
        array $properties = [],
        ?int $userId = null
    ): self {
        return static::create([
            'user_id' => $userId ?? auth()->id(),
            'type' => $type,
            'action' => $action,
            'description_ar' => $descriptionAr,
            'description_en' => $descriptionEn,
            'subject_type' => $subject ? get_class($subject) : null,
            'subject_id' => $subject?->id,
            'properties' => $properties,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Log client activity.
     */
    public static function logClient(string $action, Client $client, string $descriptionAr, ?string $descriptionEn = null, array $properties = []): self
    {
        return static::log('client', $action, $descriptionAr, $descriptionEn, $client, $properties);
    }

    /**
     * Log project activity.
     */
    public static function logProject(string $action, Project $project, string $descriptionAr, ?string $descriptionEn = null, array $properties = []): self
    {
        return static::log('project', $action, $descriptionAr, $descriptionEn, $project, $properties);
    }

    /**
     * Log task activity.
     */
    public static function logTask(string $action, ProjectTask $task, string $descriptionAr, ?string $descriptionEn = null, array $properties = []): self
    {
        return static::log('task', $action, $descriptionAr, $descriptionEn, $task, $properties);
    }

    /**
     * Log invoice activity.
     */
    public static function logInvoice(string $action, Invoice $invoice, string $descriptionAr, ?string $descriptionEn = null, array $properties = []): self
    {
        return static::log('invoice', $action, $descriptionAr, $descriptionEn, $invoice, $properties);
    }

    /**
     * Log user activity.
     */
    public static function logUser(string $action, string $descriptionAr, ?string $descriptionEn = null, ?User $user = null, array $properties = []): self
    {
        return static::log('user', $action, $descriptionAr, $descriptionEn, $user, $properties);
    }

    /**
     * Get formatted time ago.
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get activity summary for dashboard.
     */
    public static function getDashboardSummary(int $limit = 10): array
    {
        $activities = static::with(['user', 'subject'])
            ->recent($limit)
            ->get()
            ->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'type' => $activity->type,
                    'action' => $activity->action,
                    'description' => $activity->display_description,
                    'user_name' => $activity->user?->name ?? 'نظام',
                    'icon' => $activity->icon,
                    'color' => $activity->color,
                    'time_ago' => $activity->time_ago,
                    'created_at' => $activity->created_at,
                ];
            });

        return $activities->toArray();
    }

    /**
     * Get activity statistics.
     */
    public static function getStatistics(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'total_activities' => static::where('created_at', '>=', $startDate)->count(),
            'by_type' => static::where('created_at', '>=', $startDate)
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'by_action' => static::where('created_at', '>=', $startDate)
                ->selectRaw('action, COUNT(*) as count')
                ->groupBy('action')
                ->pluck('count', 'action')
                ->toArray(),
            'daily_counts' => static::where('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('count', 'date')
                ->toArray(),
        ];
    }
}
