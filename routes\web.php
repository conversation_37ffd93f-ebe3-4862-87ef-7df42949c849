<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;

Route::view('/', 'welcome');

// Role-based dashboard routing
Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Founder-only routes
Route::middleware(['auth', 'verified', 'role:founder'])->group(function () {
    Route::prefix('founder')->name('founder.')->group(function () {
        // System administration routes will be added here
    });
});

// Admin routes
Route::middleware(['auth', 'verified', 'role:founder,admin'])->group(function () {
    Route::prefix('admin')->name('admin.')->group(function () {
        // Administrative routes will be added here
    });
});

// Manager routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager'])->group(function () {
    Route::prefix('manager')->name('manager.')->group(function () {
        // Management routes will be added here
    });
});

// Employee routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager,employee'])->group(function () {
    Route::prefix('employee')->name('employee.')->group(function () {
        // Employee routes will be added here
    });
});

// Client routes
Route::middleware(['auth', 'verified', 'role:client'])->group(function () {
    Route::prefix('client')->name('client.')->group(function () {
        // Client-specific routes will be added here
    });
});

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

// API routes for dashboard
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/api/dashboard/stats', [DashboardController::class, 'getStats'])->name('api.dashboard.stats');
    Route::get('/api/dashboard/activities', [DashboardController::class, 'getActivities'])->name('api.dashboard.activities');
    Route::get('/api/dashboard/charts', [DashboardController::class, 'getChartData'])->name('api.dashboard.charts');
});

require __DIR__.'/auth.php';
