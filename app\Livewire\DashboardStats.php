<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Client;
use App\Models\Project;
use App\Models\Invoice;
use App\Models\ProjectTask;
use App\Models\Activity;
use App\Models\User;

class DashboardStats extends Component
{
    public $stats = [];
    public $recentActivities = [];
    public $refreshInterval = 30000; // 30 seconds

    public function mount()
    {
        $this->loadStats();
        $this->loadRecentActivities();
    }

    public function loadStats()
    {
        $this->stats = [
            'total_clients' => Client::count(),
            'active_clients' => Client::where('status', 'active')->count(),
            'total_projects' => Project::count(),
            'active_projects' => Project::where('status', 'active')->count(),
            'completed_projects' => Project::where('status', 'completed')->count(),
            'monthly_revenue' => Invoice::whereMonth('created_at', now()->month)
                                      ->where('payment_status', 'paid')
                                      ->sum('total_amount'),
            'yearly_revenue' => Invoice::whereYear('created_at', now()->year)
                                     ->where('payment_status', 'paid')
                                     ->sum('total_amount'),
            'team_members' => User::whereHas('roles', function($q) {
                $q->whereIn('name', ['admin', 'manager', 'employee']);
            })->count(),
            'pending_invoices' => Invoice::where('payment_status', 'unpaid')->count(),
            'overdue_invoices' => Invoice::where('payment_status', 'overdue')->count(),
            'total_invoices' => Invoice::count(),
            'pending_tasks' => ProjectTask::whereIn('status', ['todo', 'in_progress'])->count(),
            'completed_tasks' => ProjectTask::where('status', 'completed')->count(),
            'overdue_tasks' => ProjectTask::where('due_date', '<', now())
                                        ->whereNotIn('status', ['completed', 'cancelled'])
                                        ->count(),
        ];
    }

    public function loadRecentActivities()
    {
        $this->recentActivities = Activity::getDashboardSummary(10);
    }

    public function refreshStats()
    {
        $this->loadStats();
        $this->loadRecentActivities();
        
        $this->dispatch('stats-updated', $this->stats);
        $this->dispatch('activities-updated', $this->recentActivities);
    }

    public function render()
    {
        return view('livewire.dashboard-stats');
    }
}
