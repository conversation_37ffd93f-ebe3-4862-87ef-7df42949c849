<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('type', 50)->comment('Type of activity: client, project, task, invoice, payment, user, etc.');
            $table->string('action', 50)->comment('Action performed: created, updated, deleted, completed, etc.');
            $table->string('description_ar')->comment('Arabic description of the activity');
            $table->string('description_en')->nullable()->comment('English description of the activity');
            $table->string('subject_type')->nullable()->comment('Model class name');
            $table->unsignedBigInteger('subject_id')->nullable()->comment('Model ID');
            $table->json('properties')->nullable()->comment('Additional activity data');
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['type', 'action']);
            $table->index(['subject_type', 'subject_id']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activities');
    }
};
