{{-- Founder Dashboard --}}
<div class="space-y-6">
    {{-- Welcome Section --}}
    <div class="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-bold font-arabic">مرحباً بك في نظام إدارة ليال</h3>
                <p class="text-primary-100 mt-1">لوحة تحكم المؤسس - إدارة شاملة للنظام</p>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold">{{ date('Y/m/d') }}</div>
                <div class="text-primary-200 text-sm">{{ date('l') }}</div>
            </div>
        </div>
    </div>

    {{-- KPIs Section --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {{-- Revenue Growth --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.revenue_growth') }}</p>
                    <p class="text-2xl font-bold {{ $kpis['revenue_growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $kpis['revenue_growth'] >= 0 ? '+' : '' }}{{ $kpis['revenue_growth'] }}%
                    </p>
                    <p class="text-xs text-secondary-500">{{ __('app.dashboard.vs_last_month') }}</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 {{ $kpis['revenue_growth'] >= 0 ? 'bg-green-100' : 'bg-red-100' }} rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 {{ $kpis['revenue_growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            @if($kpis['revenue_growth'] >= 0)
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            @else
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            @endif
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Project Completion Rate --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.project_completion_rate') }}</p>
                    <p class="text-2xl font-bold text-blue-600">{{ $kpis['project_completion_rate'] }}%</p>
                    <p class="text-xs text-secondary-500">من إجمالي المشاريع</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Client Satisfaction --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.client_satisfaction') }}</p>
                    <p class="text-2xl font-bold text-yellow-600">{{ $kpis['client_satisfaction'] }}%</p>
                    <p class="text-xs text-secondary-500">متوسط التقييم</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Team Efficiency --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.team_efficiency') }}</p>
                    <p class="text-2xl font-bold text-purple-600">{{ $kpis['team_efficiency'] }}%</p>
                    <p class="text-xs text-secondary-500">معدل إنجاز المهام</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Enhanced Stats Grid --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {{-- Total Clients --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.total_clients') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['total_clients']) }}</p>
                    <p class="text-xs text-green-600">{{ number_format($stats['active_clients']) }} نشط</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Projects Overview --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.total_projects') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['total_projects']) }}</p>
                    <p class="text-xs text-green-600">{{ number_format($stats['active_projects']) }} نشط</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Revenue Overview --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.monthly_revenue') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['monthly_revenue'], 0) }} ريال</p>
                    <p class="text-xs text-secondary-500">{{ number_format($stats['yearly_revenue'], 0) }} ريال سنوياً</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Tasks Overview --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.pending_tasks') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['pending_tasks']) }}</p>
                    <p class="text-xs text-red-600">{{ number_format($stats['overdue_tasks']) }} متأخرة</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Charts Section --}}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {{-- Revenue Trend Chart --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-lg font-semibold text-secondary-900 font-arabic">{{ __('app.dashboard.revenue_trend') }}</h4>
                <div class="text-sm text-secondary-500">آخر 12 شهر</div>
            </div>
            <div class="h-64">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        {{-- Project Status Distribution --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-lg font-semibold text-secondary-900 font-arabic">{{ __('app.dashboard.project_status_distribution') }}</h4>
                <div class="text-sm text-secondary-500">الحالة الحالية</div>
            </div>
            <div class="h-64">
                <canvas id="projectStatusChart"></canvas>
            </div>
        </div>
    </div>

    {{-- Enhanced Quick Actions --}}
    <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-semibold text-secondary-900 font-arabic">{{ __('app.dashboard.quick_actions') }}</h4>
            <a href="#" class="text-sm text-primary-600 hover:text-primary-700">{{ __('app.dashboard.view_all') }}</a>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <a href="#" class="group flex flex-col items-center p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-all duration-200 hover:scale-105">
                <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-primary-200 transition-colors">
                    <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <span class="text-sm font-medium text-primary-700 text-center">{{ __('app.dashboard.add_client') }}</span>
            </a>

            <a href="#" class="group flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-all duration-200 hover:scale-105">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-green-200 transition-colors">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <span class="text-sm font-medium text-green-700 text-center">{{ __('app.dashboard.new_project') }}</span>
            </a>

            <a href="#" class="group flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-all duration-200 hover:scale-105">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-yellow-200 transition-colors">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <span class="text-sm font-medium text-yellow-700 text-center">{{ __('app.dashboard.new_invoice') }}</span>
            </a>

            <a href="#" class="group flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-all duration-200 hover:scale-105">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-purple-200 transition-colors">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <span class="text-sm font-medium text-purple-700 text-center">{{ __('app.dashboard.add_team_member') }}</span>
            </a>

            <a href="#" class="group flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-all duration-200 hover:scale-105">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-200 transition-colors">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <span class="text-sm font-medium text-blue-700 text-center">{{ __('app.dashboard.analytics') }}</span>
            </a>

            <a href="#" class="group flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-200 hover:scale-105">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-gray-200 transition-colors">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <span class="text-sm font-medium text-gray-700 text-center">{{ __('app.dashboard.manage') }}</span>
            </a>
        </div>
    </div>

    {{-- Real-time Activity Feed --}}
    <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-semibold text-secondary-900 font-arabic">{{ __('app.dashboard.recent_activity') }}</h4>
            <div class="flex items-center space-x-2 space-x-reverse">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-secondary-500">مباشر</span>
            </div>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
            @forelse($recentActivities as $activity)
                <div class="flex items-start p-3 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-colors">
                    <div class="flex-shrink-0 ml-3">
                        <div class="w-8 h-8 bg-{{ $activity['color'] }}-100 rounded-lg flex items-center justify-center">
                            <div class="w-2 h-2 bg-{{ $activity['color'] }}-500 rounded-full"></div>
                        </div>
                    </div>
                    <div class="flex-1 text-right">
                        <p class="text-sm text-secondary-900 font-medium">{{ $activity['description'] }}</p>
                        <div class="flex items-center justify-between mt-1">
                            <span class="text-xs text-secondary-500">{{ $activity['time_ago'] }}</span>
                            <span class="text-xs text-secondary-600">{{ $activity['user_name'] }}</span>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <p class="text-secondary-500">لا توجد أنشطة حديثة</p>
                </div>
            @endforelse
        </div>
        @if(count($recentActivities) > 0)
            <div class="mt-4 pt-4 border-t border-secondary-200">
                <a href="#" class="block text-center text-sm text-primary-600 hover:text-primary-700 font-medium">
                    عرض جميع الأنشطة
                </a>
            </div>
        @endif
    </div>
</div>

{{-- Chart.js and Dashboard Scripts --}}
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@vite('resources/js/dashboard.js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts with initial data
    const chartData = @json($chartData);

    // Revenue Trend Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx.getContext('2d'), {
            type: 'line',
            data: {
                labels: chartData.revenue_trend.labels,
                datasets: [{
                    label: 'الإيرادات (ريال)',
                    data: chartData.revenue_trend.data,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('ar-SA').format(value) + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }

    // Project Status Chart
    const projectCtx = document.getElementById('projectStatusChart');
    if (projectCtx) {
        new Chart(projectCtx.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: chartData.project_status.labels,
                datasets: [{
                    data: chartData.project_status.data,
                    backgroundColor: [
                        '#3B82F6', // blue
                        '#10B981', // green
                        '#F59E0B', // yellow
                        '#EF4444', // red
                        '#8B5CF6'  // purple
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
});
</script>
@endpush
