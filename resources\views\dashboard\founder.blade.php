{{-- Founder Dashboard --}}
<div class="space-y-6">
    {{-- Welcome Section --}}
    <div class="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-bold font-arabic">مرحباً بك في نظام إدارة ليال</h3>
                <p class="text-primary-100 mt-1">لوحة تحكم المؤسس - إدارة شاملة للنظام</p>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold">{{ date('Y/m/d') }}</div>
                <div class="text-primary-200 text-sm">{{ date('l') }}</div>
            </div>
        </div>
    </div>

    {{-- Quick Stats Grid --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {{-- Total Clients --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.total_clients') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['total_clients']) }}</p>
                </div>
            </div>
        </div>

        {{-- Active Projects --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.active_projects') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['active_projects']) }}</p>
                </div>
            </div>
        </div>

        {{-- Monthly Revenue --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.monthly_revenue') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['monthly_revenue'], 2) }} ريال</p>
                </div>
            </div>
        </div>

        {{-- Team Members --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.team_members') }}</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['team_members']) }}</p>
                </div>
            </div>
        </div>

        {{-- Pending Invoices --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 text-right">
                    <p class="text-sm font-medium text-secondary-600">الفواتير المعلقة</p>
                    <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['pending_invoices']) }}</p>
                </div>
            </div>
        </div>

        {{-- Overdue Invoices --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 text-right">
                    <p class="text-sm font-medium text-secondary-600">الفواتير المتأخرة</p>
                    <p class="text-2xl font-bold text-red-600">{{ number_format($stats['overdue_invoices']) }}</p>
                </div>
            </div>
        </div>
    </div>

    {{-- Quick Actions --}}
    <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
        <h4 class="text-lg font-semibold text-secondary-900 mb-4 font-arabic">{{ __('app.dashboard.quick_actions') }}</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="#" class="flex flex-col items-center p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors">
                <svg class="w-8 h-8 text-primary-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span class="text-sm font-medium text-primary-700">إضافة عميل</span>
            </a>
            <a href="#" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                <svg class="w-8 h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <span class="text-sm font-medium text-green-700">مشروع جديد</span>
            </a>
            <a href="#" class="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                <svg class="w-8 h-8 text-yellow-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-sm font-medium text-yellow-700">فاتورة جديدة</span>
            </a>
            <a href="#" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                <svg class="w-8 h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-sm font-medium text-purple-700">عضو فريق</span>
            </a>
        </div>
    </div>

    {{-- Recent Activity --}}
    <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
        <h4 class="text-lg font-semibold text-secondary-900 mb-4 font-arabic">{{ __('app.dashboard.recent_activity') }}</h4>
        <div class="space-y-4">
            <div class="flex items-center p-3 bg-secondary-50 rounded-lg">
                <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full"></div>
                <div class="mr-3 text-right">
                    <p class="text-sm text-secondary-900">تم إنشاء مشروع جديد: تطوير موقع إلكتروني</p>
                    <p class="text-xs text-secondary-500">منذ ساعتين</p>
                </div>
            </div>
            <div class="flex items-center p-3 bg-secondary-50 rounded-lg">
                <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                <div class="mr-3 text-right">
                    <p class="text-sm text-secondary-900">تم إضافة عميل جديد: شركة التقنية المتقدمة</p>
                    <p class="text-xs text-secondary-500">منذ 4 ساعات</p>
                </div>
            </div>
            <div class="flex items-center p-3 bg-secondary-50 rounded-lg">
                <div class="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div class="mr-3 text-right">
                    <p class="text-sm text-secondary-900">تم إرسال فاتورة رقم INV-2024-001</p>
                    <p class="text-xs text-secondary-500">أمس</p>
                </div>
            </div>
        </div>
    </div>
</div>
