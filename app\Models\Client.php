<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Client extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'client_code',
        'type',
        'name_ar',
        'name_en',
        'email',
        'email_verified_at',
        'phone',
        'mobile',
        'whatsapp',
        'birth_date',
        'gender',
        'nationality',
        'id_number',
        'company_name_ar',
        'company_name_en',
        'commercial_register',
        'tax_number',
        'industry',
        'company_size',
        'website',
        'country',
        'city',
        'district',
        'address',
        'postal_code',
        'latitude',
        'longitude',
        'status',
        'priority',
        'source',
        'referral_source',
        'credit_limit',
        'payment_terms',
        'notes',
        'tags',
        'custom_fields',
        'assigned_to',
        'created_by',
        'updated_by',
        'last_contact_at',
        'next_follow_up_at',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'birth_date' => 'date',
        'company_size' => 'integer',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'credit_limit' => 'decimal:2',
        'payment_terms' => 'integer',
        'tags' => 'array',
        'custom_fields' => 'array',
        'last_contact_at' => 'datetime',
        'next_follow_up_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
            if (empty($model->client_code)) {
                $model->client_code = 'CL-' . str_pad(static::count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }

    /**
     * Get the user assigned to this client.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who created this client.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this client.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the contacts for this client.
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(ClientContact::class);
    }

    /**
     * Get active contacts for this client.
     */
    public function activeContacts(): HasMany
    {
        return $this->contacts()->where('is_active', true);
    }

    /**
     * Get the communications for this client.
     */
    public function communications(): HasMany
    {
        return $this->hasMany(ClientCommunication::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get the documents for this client.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(ClientDocument::class);
    }

    /**
     * Get the projects for this client.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the invoices for this client.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the payments for this client.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Scope to get only active clients.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to filter by type.
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by priority.
     */
    public function scopePriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Get the display name based on current locale and type.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->type === 'company') {
            return app()->getLocale() === 'ar' 
                ? ($this->company_name_ar ?? $this->name_ar) 
                : ($this->company_name_en ?? $this->name_en ?? $this->company_name_ar ?? $this->name_ar);
        }
        
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->district,
            $this->city,
            $this->country,
            $this->postal_code,
        ]);
        
        return implode(', ', $parts);
    }

    /**
     * Get the primary contact method.
     */
    public function getPrimaryContactAttribute(): string
    {
        return $this->mobile ?? $this->phone ?? $this->email ?? 'غير محدد';
    }

    /**
     * Check if client needs follow up.
     */
    public function needsFollowUp(): bool
    {
        return $this->next_follow_up_at && $this->next_follow_up_at->isPast();
    }

    /**
     * Get total outstanding balance.
     */
    public function getOutstandingBalanceAttribute(): float
    {
        return $this->invoices()
            ->whereIn('payment_status', ['unpaid', 'partially_paid'])
            ->sum('balance_due');
    }

    /**
     * Get total project value.
     */
    public function getTotalProjectValueAttribute(): float
    {
        return $this->projects()->sum('budget');
    }
}
