<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class TechnicalService extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'features_ar',
        'features_en',
        'category_id',
        'base_price',
        'estimated_hours',
        'estimated_days',
        'complexity_level',
        'status',
        'requirements',
        'deliverables',
        'technologies',
        'image',
        'gallery',
        'is_featured',
        'is_customizable',
        'sort_order',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'base_price' => 'decimal:2',
        'estimated_hours' => 'integer',
        'estimated_days' => 'integer',
        'requirements' => 'array',
        'deliverables' => 'array',
        'technologies' => 'array',
        'gallery' => 'array',
        'is_featured' => 'boolean',
        'is_customizable' => 'boolean',
        'sort_order' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the category that owns the service.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class, 'category_id');
    }

    /**
     * Get the user who created the service.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the service.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the pricing tiers for this service.
     */
    public function pricingTiers(): HasMany
    {
        return $this->hasMany(ServicePricingTier::class, 'service_id')->orderBy('sort_order');
    }

    /**
     * Get active pricing tiers for this service.
     */
    public function activePricingTiers(): HasMany
    {
        return $this->pricingTiers()->where('is_active', true);
    }

    /**
     * Get the templates for this service.
     */
    public function templates(): HasMany
    {
        return $this->hasMany(ServiceTemplate::class, 'service_id');
    }

    /**
     * Get active templates for this service.
     */
    public function activeTemplates(): HasMany
    {
        return $this->templates()->where('is_active', true);
    }

    /**
     * Get projects using this service.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class, 'service_id');
    }

    /**
     * Get invoice items for this service.
     */
    public function invoiceItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class, 'service_id');
    }

    /**
     * Scope to get only active services.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get only featured services.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by complexity level.
     */
    public function scopeComplexity($query, $level)
    {
        return $query->where('complexity_level', $level);
    }

    /**
     * Get the display name based on current locale.
     */
    public function getDisplayNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    /**
     * Get the display description based on current locale.
     */
    public function getDisplayDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : ($this->description_en ?? $this->description_ar);
    }

    /**
     * Get the display features based on current locale.
     */
    public function getDisplayFeaturesAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->features_ar : ($this->features_en ?? $this->features_ar);
    }

    /**
     * Get formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->base_price, 2) . ' ريال';
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the minimum price from pricing tiers.
     */
    public function getMinPriceAttribute(): float
    {
        $minTierPrice = $this->activePricingTiers()->min('price') ?? 0;
        return min($this->base_price, $minTierPrice);
    }

    /**
     * Get the maximum price from pricing tiers.
     */
    public function getMaxPriceAttribute(): float
    {
        $maxTierPrice = $this->activePricingTiers()->max('price') ?? 0;
        return max($this->base_price, $maxTierPrice);
    }

    /**
     * Check if service has multiple pricing options.
     */
    public function hasMultiplePricing(): bool
    {
        return $this->activePricingTiers()->count() > 0;
    }

    /**
     * Get the popular pricing tier.
     */
    public function getPopularTier()
    {
        return $this->activePricingTiers()->where('is_popular', true)->first();
    }

    /**
     * Get estimated duration in human readable format.
     */
    public function getEstimatedDurationAttribute(): string
    {
        if ($this->estimated_days) {
            return $this->estimated_days . ' يوم';
        }
        
        if ($this->estimated_hours) {
            return $this->estimated_hours . ' ساعة';
        }
        
        return 'غير محدد';
    }
}
