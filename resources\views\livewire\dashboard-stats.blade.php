<div wire:poll.{{ $refreshInterval }}ms="refreshStats">
    {{-- Real-time Stats Grid --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {{-- Total Clients --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.total_clients') }}</p>
                    <p class="text-2xl font-bold text-secondary-900" wire:loading.class="animate-pulse">
                        {{ number_format($stats['total_clients'] ?? 0) }}
                    </p>
                    <p class="text-xs text-green-600">{{ number_format($stats['active_clients'] ?? 0) }} نشط</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Projects Overview --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.total_projects') }}</p>
                    <p class="text-2xl font-bold text-secondary-900" wire:loading.class="animate-pulse">
                        {{ number_format($stats['total_projects'] ?? 0) }}
                    </p>
                    <p class="text-xs text-green-600">{{ number_format($stats['active_projects'] ?? 0) }} نشط</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Revenue Overview --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.monthly_revenue') }}</p>
                    <p class="text-2xl font-bold text-secondary-900" wire:loading.class="animate-pulse">
                        {{ number_format($stats['monthly_revenue'] ?? 0, 0) }} ريال
                    </p>
                    <p class="text-xs text-secondary-500">{{ number_format($stats['yearly_revenue'] ?? 0, 0) }} ريال سنوياً</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Tasks Overview --}}
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">{{ __('app.dashboard.pending_tasks') }}</p>
                    <p class="text-2xl font-bold text-secondary-900" wire:loading.class="animate-pulse">
                        {{ number_format($stats['pending_tasks'] ?? 0) }}
                    </p>
                    <p class="text-xs text-red-600">{{ number_format($stats['overdue_tasks'] ?? 0) }} متأخرة</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Real-time Activity Feed --}}
    <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6 mt-6">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-semibold text-secondary-900 font-arabic">{{ __('app.dashboard.recent_activity') }}</h4>
            <div class="flex items-center space-x-2 space-x-reverse">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-secondary-500">مباشر</span>
                <div wire:loading wire:target="refreshStats" class="ml-2">
                    <svg class="animate-spin h-4 w-4 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
            </div>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
            @forelse($recentActivities as $activity)
                <div class="flex items-start p-3 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-colors">
                    <div class="flex-shrink-0 ml-3">
                        <div class="w-8 h-8 bg-{{ $activity['color'] }}-100 rounded-lg flex items-center justify-center">
                            <div class="w-2 h-2 bg-{{ $activity['color'] }}-500 rounded-full"></div>
                        </div>
                    </div>
                    <div class="flex-1 text-right">
                        <p class="text-sm text-secondary-900 font-medium">{{ $activity['description'] }}</p>
                        <div class="flex items-center justify-between mt-1">
                            <span class="text-xs text-secondary-500">{{ $activity['time_ago'] }}</span>
                            <span class="text-xs text-secondary-600">{{ $activity['user_name'] }}</span>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <p class="text-secondary-500">لا توجد أنشطة حديثة</p>
                </div>
            @endforelse
        </div>
        @if(count($recentActivities) > 0)
            <div class="mt-4 pt-4 border-t border-secondary-200">
                <a href="#" class="block text-center text-sm text-primary-600 hover:text-primary-700 font-medium">
                    عرض جميع الأنشطة
                </a>
            </div>
        @endif
    </div>
</div>
