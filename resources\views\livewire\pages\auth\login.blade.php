<?php

use App\Livewire\Forms\LoginForm;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.guest')] class extends Component
{
    public LoginForm $form;

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->form->authenticate();

        Session::regenerate();

        $this->redirectIntended(default: route('dashboard', absolute: false), navigate: true);
    }
}; ?>

<div>
    {{-- Welcome Message --}}
    <div class="text-center mb-6">
        <p class="text-secondary-600 text-sm">{{ __('app.auth.login_subtitle') }}</p>
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <form wire:submit="login" class="space-y-6">
        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('app.auth.email')" />
            <x-text-input wire:model="form.email" id="email" class="mt-1" type="email" name="email"
                         placeholder="{{ __('app.auth.email') }}" required autofocus autocomplete="username" />
            <x-input-error :messages="$errors->get('form.email')" class="mt-2" />
        </div>

        <!-- Password -->
        <div>
            <x-input-label for="password" :value="__('app.auth.password')" />
            <x-text-input wire:model="form.password" id="password" class="mt-1"
                         type="password" name="password" placeholder="{{ __('app.auth.password') }}"
                         required autocomplete="current-password" />
            <x-input-error :messages="$errors->get('form.password')" class="mt-2" />
        </div>

        <!-- Remember Me -->
        <div class="flex items-center justify-between">
            <label for="remember" class="inline-flex items-center">
                <input wire:model="form.remember" id="remember" type="checkbox"
                       class="rounded border-secondary-300 text-primary-600 shadow-sm focus:ring-primary-500" name="remember">
                <span class="mr-2 text-sm text-secondary-600 font-arabic">{{ __('app.auth.remember_me') }}</span>
            </label>

            @if (Route::has('password.request'))
                <a class="text-sm text-primary-600 hover:text-primary-700 font-arabic" href="{{ route('password.request') }}" wire:navigate>
                    {{ __('app.auth.forgot_password') }}
                </a>
            @endif
        </div>

        <div class="space-y-4">
            <x-primary-button>
                {{ __('app.auth.login') }}
            </x-primary-button>

            @if (Route::has('register'))
                <div class="text-center">
                    <span class="text-sm text-secondary-600">{{ __('app.auth.not_registered') }}</span>
                    <a href="{{ route('register') }}" wire:navigate class="text-sm text-primary-600 hover:text-primary-700 font-medium">
                        {{ __('app.auth.register') }}
                    </a>
                </div>
            @endif
        </div>
    </form>
</div>
