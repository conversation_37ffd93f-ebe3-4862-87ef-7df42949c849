/**
 * Dashboard Real-time Updates
 */

class DashboardManager {
    constructor() {
        this.refreshInterval = 30000; // 30 seconds
        this.charts = {};
        this.intervalId = null;
        
        this.init();
    }

    init() {
        this.initializeCharts();
        this.startAutoRefresh();
        this.bindEvents();
    }

    initializeCharts() {
        // Revenue Trend Chart
        const revenueCtx = document.getElementById('revenueChart');
        if (revenueCtx) {
            this.charts.revenue = new Chart(revenueCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'الإيرادات (ريال)',
                        data: [],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('ar-SA').format(value) + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Project Status Chart
        const projectCtx = document.getElementById('projectStatusChart');
        if (projectCtx) {
            this.charts.projectStatus = new Chart(projectCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#3B82F6', // blue
                            '#10B981', // green
                            '#F59E0B', // yellow
                            '#EF4444', // red
                            '#8B5CF6'  // purple
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }
    }

    async refreshStats() {
        try {
            const response = await fetch('/api/dashboard/stats');
            const stats = await response.json();
            
            this.updateStatsDisplay(stats);
        } catch (error) {
            console.error('Error refreshing stats:', error);
        }
    }

    async refreshActivities() {
        try {
            const response = await fetch('/api/dashboard/activities');
            const activities = await response.json();
            
            this.updateActivitiesDisplay(activities);
        } catch (error) {
            console.error('Error refreshing activities:', error);
        }
    }

    async refreshCharts() {
        try {
            const response = await fetch('/api/dashboard/charts');
            const chartData = await response.json();
            
            this.updateChartsDisplay(chartData);
        } catch (error) {
            console.error('Error refreshing charts:', error);
        }
    }

    updateStatsDisplay(stats) {
        // Update stat values with animation
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
                const newValue = stats[key];
                
                this.animateValue(element, currentValue, newValue);
            }
        });
    }

    updateActivitiesDisplay(activities) {
        const container = document.querySelector('[data-activities-container]');
        if (!container) return;

        container.innerHTML = '';
        
        if (activities.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <p class="text-secondary-500">لا توجد أنشطة حديثة</p>
                </div>
            `;
            return;
        }

        activities.forEach(activity => {
            const activityElement = document.createElement('div');
            activityElement.className = 'flex items-start p-3 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-colors';
            activityElement.innerHTML = `
                <div class="flex-shrink-0 ml-3">
                    <div class="w-8 h-8 bg-${activity.color}-100 rounded-lg flex items-center justify-center">
                        <div class="w-2 h-2 bg-${activity.color}-500 rounded-full"></div>
                    </div>
                </div>
                <div class="flex-1 text-right">
                    <p class="text-sm text-secondary-900 font-medium">${activity.description}</p>
                    <div class="flex items-center justify-between mt-1">
                        <span class="text-xs text-secondary-500">${activity.time_ago}</span>
                        <span class="text-xs text-secondary-600">${activity.user_name}</span>
                    </div>
                </div>
            `;
            container.appendChild(activityElement);
        });
    }

    updateChartsDisplay(chartData) {
        // Update revenue chart
        if (this.charts.revenue && chartData.revenue_trend) {
            this.charts.revenue.data.labels = chartData.revenue_trend.labels;
            this.charts.revenue.data.datasets[0].data = chartData.revenue_trend.data;
            this.charts.revenue.update('none');
        }

        // Update project status chart
        if (this.charts.projectStatus && chartData.project_status) {
            this.charts.projectStatus.data.labels = chartData.project_status.labels;
            this.charts.projectStatus.data.datasets[0].data = chartData.project_status.data;
            this.charts.projectStatus.update('none');
        }
    }

    animateValue(element, start, end, duration = 1000) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                current = end;
                clearInterval(timer);
            }
            
            element.textContent = this.formatNumber(Math.floor(current));
        }, 16);
    }

    formatNumber(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    }

    startAutoRefresh() {
        this.intervalId = setInterval(() => {
            this.refreshStats();
            this.refreshActivities();
            this.refreshCharts();
        }, this.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    bindEvents() {
        // Manual refresh button
        const refreshButton = document.querySelector('[data-refresh-dashboard]');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.refreshStats();
                this.refreshActivities();
                this.refreshCharts();
            });
        }

        // Pause/resume auto-refresh on visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else {
                this.startAutoRefresh();
            }
        });
    }

    destroy() {
        this.stopAutoRefresh();
        
        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('#revenueChart') || document.querySelector('#projectStatusChart')) {
        window.dashboardManager = new DashboardManager();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.dashboardManager) {
        window.dashboardManager.destroy();
    }
});
