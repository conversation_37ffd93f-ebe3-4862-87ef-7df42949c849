<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServicePricingTier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'service_id',
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'price',
        'estimated_hours',
        'estimated_days',
        'features',
        'limitations',
        'is_popular',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'estimated_hours' => 'integer',
        'estimated_days' => 'integer',
        'features' => 'array',
        'limitations' => 'array',
        'is_popular' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the service that owns the pricing tier.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(TechnicalService::class, 'service_id');
    }

    /**
     * Scope to get only active tiers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only popular tiers.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Get the display name based on current locale.
     */
    public function getDisplayNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    /**
     * Get the display description based on current locale.
     */
    public function getDisplayDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : ($this->description_en ?? $this->description_ar);
    }

    /**
     * Get formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price, 2) . ' ريال';
    }

    /**
     * Get estimated duration in human readable format.
     */
    public function getEstimatedDurationAttribute(): string
    {
        if ($this->estimated_days) {
            return $this->estimated_days . ' يوم';
        }
        
        if ($this->estimated_hours) {
            return $this->estimated_hours . ' ساعة';
        }
        
        return 'غير محدد';
    }
}
