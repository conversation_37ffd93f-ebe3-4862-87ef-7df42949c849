<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'service_id',
        'item_name_ar',
        'item_name_en',
        'description_ar',
        'description_en',
        'quantity',
        'unit_ar',
        'unit_en',
        'unit_price',
        'discount_rate',
        'discount_amount',
        'tax_rate',
        'tax_amount',
        'total_amount',
        'sort_order',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    /**
     * Get the invoice that owns the item.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the service associated with the item.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(TechnicalService::class, 'service_id');
    }

    /**
     * Get the item's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->item_name_ar ?? $this->item_name_en ?? 'عنصر بدون اسم';
    }

    /**
     * Calculate the line total.
     */
    public function calculateTotal(): float
    {
        $subtotal = $this->quantity * $this->unit_price;
        $discountAmount = ($subtotal * $this->discount_rate) / 100;
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = ($taxableAmount * $this->tax_rate) / 100;
        
        return $taxableAmount + $taxAmount;
    }

    /**
     * Update the calculated amounts.
     */
    public function updateCalculatedAmounts(): void
    {
        $subtotal = $this->quantity * $this->unit_price;
        $discountAmount = ($subtotal * $this->discount_rate) / 100;
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = ($taxableAmount * $this->tax_rate) / 100;
        $totalAmount = $taxableAmount + $taxAmount;

        $this->update([
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }
}
