{{-- Top Navigation Bar --}}
<div class="bg-white shadow-sm border-b border-secondary-200 lg:hidden">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
            {{-- Mobile menu button --}}
            <button @click="sidebarOpen = !sidebarOpen" 
                    class="inline-flex items-center justify-center p-2 rounded-md text-secondary-400 hover:text-secondary-500 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                <span class="sr-only">فتح القائمة الرئيسية</span>
                <x-icon name="menu" class="h-6 w-6" x-show="!sidebarOpen" />
                <x-icon name="x" class="h-6 w-6" x-show="sidebarOpen" x-cloak />
            </button>

            {{-- Logo for mobile --}}
            <div class="flex items-center">
                <x-application-logo class="h-8 w-auto" />
            </div>

            {{-- Mobile user menu --}}
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" 
                        class="flex items-center p-2 rounded-md text-secondary-400 hover:text-secondary-500 hover:bg-secondary-100">
                    <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                        <span class="text-xs font-medium text-white">
                            {{ substr(auth()->user()->name, 0, 2) }}
                        </span>
                    </div>
                </button>

                {{-- Mobile dropdown menu --}}
                <div x-show="open" 
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
                     x-cloak>
                    <div class="py-1">
                        <a href="{{ route('profile') }}" 
                           class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 text-right font-arabic">
                            الملف الشخصي
                        </a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" 
                                    class="block w-full text-right px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 font-arabic">
                                تسجيل الخروج
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Desktop top bar --}}
<div class="hidden lg:block bg-white shadow-sm border-b border-secondary-200">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
            {{-- Search bar --}}
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <x-icon name="search" class="h-5 w-5 text-secondary-400" />
                    </div>
                    <input type="text" 
                           placeholder="البحث في النظام..." 
                           class="block w-full pr-10 pl-3 py-2 border border-secondary-300 rounded-lg leading-5 bg-white placeholder-secondary-500 focus:outline-none focus:placeholder-secondary-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-right font-arabic">
                </div>
            </div>

            {{-- Right side items --}}
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                {{-- Notifications --}}
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="relative p-2 text-secondary-400 hover:text-secondary-500 hover:bg-secondary-100 rounded-lg">
                        <x-icon name="bell" class="h-6 w-6" />
                        {{-- Notification badge --}}
                        <span class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
                    </button>

                    {{-- Notifications dropdown --}}
                    <div x-show="open" 
                         @click.away="open = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
                         x-cloak>
                        <div class="p-4">
                            <h3 class="text-lg font-medium text-secondary-900 text-right font-arabic">الإشعارات</h3>
                            <div class="mt-4 space-y-3">
                                <div class="flex items-start p-3 bg-secondary-50 rounded-lg">
                                    <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                    <div class="mr-3 text-right">
                                        <p class="text-sm text-secondary-900 font-arabic">تم إضافة مشروع جديد</p>
                                        <p class="text-xs text-secondary-500">منذ 5 دقائق</p>
                                    </div>
                                </div>
                                <div class="flex items-start p-3 bg-secondary-50 rounded-lg">
                                    <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                    <div class="mr-3 text-right">
                                        <p class="text-sm text-secondary-900 font-arabic">تم دفع فاتورة</p>
                                        <p class="text-xs text-secondary-500">منذ ساعة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 text-center">
                                <a href="#" class="text-sm text-primary-600 hover:text-primary-700 font-arabic">
                                    عرض جميع الإشعارات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- User menu --}}
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="flex items-center p-2 rounded-lg text-secondary-700 hover:bg-secondary-100">
                        <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center ml-2">
                            <span class="text-sm font-medium text-white">
                                {{ substr(auth()->user()->name, 0, 2) }}
                            </span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-secondary-900">{{ auth()->user()->name }}</div>
                        </div>
                        <x-icon name="chevron-down" class="mr-2 h-4 w-4" />
                    </button>

                    {{-- User dropdown menu --}}
                    <div x-show="open" 
                         @click.away="open = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
                         x-cloak>
                        <div class="py-1">
                            <a href="{{ route('profile') }}" 
                               class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 text-right font-arabic">
                                الملف الشخصي
                            </a>
                            <a href="#" 
                               class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 text-right font-arabic">
                                الإعدادات
                            </a>
                            <div class="border-t border-secondary-100"></div>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" 
                                        class="block w-full text-right px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 font-arabic">
                                    تسجيل الخروج
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
