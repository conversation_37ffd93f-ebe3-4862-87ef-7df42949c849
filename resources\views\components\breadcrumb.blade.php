@props(['items' => []])

@php
// Auto-generate breadcrumb items based on current route if not provided
if (empty($items)) {
    $routeName = request()->route()->getName();
    $segments = explode('.', $routeName);
    
    $items = [];
    $currentPath = '';
    
    foreach ($segments as $index => $segment) {
        $currentPath .= ($index > 0 ? '.' : '') . $segment;
        
        // Skip the last segment as it's the current page
        if ($index === count($segments) - 1) {
            continue;
        }
        
        $label = match($segment) {
            'dashboard' => __('app.navigation.dashboard'),
            'clients' => __('app.navigation.clients'),
            'projects' => __('app.navigation.projects'),
            'finance' => __('app.navigation.finance'),
            'team' => __('app.navigation.team'),
            'services' => __('app.navigation.technical_services'),
            'reports' => __('app.navigation.reports'),
            'settings' => __('app.navigation.settings'),
            default => ucfirst($segment)
        };
        
        $items[] = [
            'label' => $label,
            'url' => route($currentPath),
        ];
    }
}
@endphp

@if(!empty($items))
<nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-2 rtl:space-x-reverse">
        {{-- Home/Dashboard link --}}
        <li>
            <div>
                <a href="{{ route('dashboard') }}" class="text-secondary-400 hover:text-secondary-500">
                    <x-icon name="home" class="flex-shrink-0 h-4 w-4" />
                    <span class="sr-only">{{ __('app.navigation.dashboard') }}</span>
                </a>
            </div>
        </li>

        {{-- Breadcrumb items --}}
        @foreach($items as $item)
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-4 w-4 text-secondary-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ $item['url'] }}" class="text-sm font-medium text-secondary-500 hover:text-secondary-700 font-arabic">
                        {{ $item['label'] }}
                    </a>
                </div>
            </li>
        @endforeach

        {{-- Current page --}}
        <li>
            <div class="flex items-center">
                <svg class="flex-shrink-0 h-4 w-4 text-secondary-300 mx-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm font-medium text-secondary-900 font-arabic">
                    @if(request()->route()->getName() === 'dashboard')
                        {{ __('app.navigation.dashboard') }}
                    @else
                        {{ $title ?? 'الصفحة الحالية' }}
                    @endif
                </span>
            </div>
        </li>
    </ol>
</nav>
@endif
