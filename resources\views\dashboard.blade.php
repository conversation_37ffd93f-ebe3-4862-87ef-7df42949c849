<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-secondary-800 leading-tight font-arabic">
                {{ __('app.dashboard.title') }}
            </h2>
            <div class="text-sm text-secondary-600 font-arabic">
                {{ __('app.dashboard.welcome_message') }}, {{ $user->name }}
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            {{-- Role-specific dashboard content --}}
            @if($dashboardType === 'founder')
                @include('dashboard.founder', ['stats' => $stats])
            @elseif($dashboardType === 'admin')
                @include('dashboard.admin', ['stats' => $stats])
            @elseif($dashboardType === 'manager')
                @include('dashboard.manager', ['stats' => $stats])
            @elseif($dashboardType === 'employee')
                @include('dashboard.employee', ['stats' => $stats])
            @elseif($dashboardType === 'client')
                @include('dashboard.client', ['stats' => $stats, 'client' => $client ?? null])
            @else
                @include('dashboard.default')
            @endif
        </div>
    </div>
</x-app-layout>
