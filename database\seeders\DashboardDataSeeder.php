<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Client;
use App\Models\Project;
use App\Models\Invoice;
use App\Models\ProjectTask;
use App\Models\Activity;
use App\Models\User;
use Spatie\Permission\Models\Role;

class DashboardDataSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Create sample clients
        $clients = [
            [
                'name_ar' => 'شركة التقنية المتقدمة',
                'name_en' => 'Advanced Technology Company',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
                'status' => 'active',
                'type' => 'company',
                'company_name_ar' => 'شركة التقنية المتقدمة',
                'company_name_en' => 'Advanced Technology Company',
                'created_by' => 1,
            ],
            [
                'name_ar' => 'مؤسسة الابتكار الرقمي',
                'name_en' => 'Digital Innovation Foundation',
                'email' => '<EMAIL>',
                'phone' => '+966502345678',
                'status' => 'active',
                'type' => 'company',
                'company_name_ar' => 'مؤسسة الابتكار الرقمي',
                'company_name_en' => 'Digital Innovation Foundation',
                'created_by' => 1,
            ],
            [
                'name_ar' => 'شركة الحلول الذكية',
                'name_en' => 'Smart Solutions Company',
                'email' => '<EMAIL>',
                'phone' => '+966503456789',
                'status' => 'active',
                'type' => 'company',
                'company_name_ar' => 'شركة الحلول الذكية',
                'company_name_en' => 'Smart Solutions Company',
                'created_by' => 1,
            ],
        ];

        foreach ($clients as $clientData) {
            $client = Client::create($clientData);
            
            // Log client creation activity
            Activity::logClient('created', $client, "تم إنشاء عميل جديد: {$client->name_ar}", "New client created: {$client->name_en}");
        }

        // Create sample projects
        $projects = [
            [
                'client_id' => 1,
                'name_ar' => 'تطوير موقع إلكتروني متقدم',
                'name_en' => 'Advanced Website Development',
                'description_ar' => 'تطوير موقع إلكتروني متقدم مع نظام إدارة المحتوى',
                'description_en' => 'Advanced website development with content management system',
                'start_date' => now()->subDays(30),
                'end_date' => now()->addDays(30),
                'budget' => 50000,
                'status' => 'active',
                'priority' => 'high',
                'progress_percentage' => 65,
                'project_manager_id' => 1,
                'created_by' => 1,
            ],
            [
                'client_id' => 2,
                'name_ar' => 'تطبيق جوال للتجارة الإلكترونية',
                'name_en' => 'E-commerce Mobile Application',
                'description_ar' => 'تطوير تطبيق جوال للتجارة الإلكترونية مع نظام دفع متكامل',
                'description_en' => 'E-commerce mobile app development with integrated payment system',
                'start_date' => now()->subDays(45),
                'end_date' => now()->addDays(15),
                'budget' => 75000,
                'status' => 'active',
                'priority' => 'urgent',
                'progress_percentage' => 80,
                'project_manager_id' => 1,
                'created_by' => 1,
            ],
            [
                'client_id' => 3,
                'name_ar' => 'نظام إدارة المخزون',
                'name_en' => 'Inventory Management System',
                'description_ar' => 'تطوير نظام إدارة المخزون مع تقارير تفصيلية',
                'description_en' => 'Inventory management system development with detailed reporting',
                'start_date' => now()->subDays(60),
                'end_date' => now()->subDays(10),
                'budget' => 40000,
                'status' => 'completed',
                'priority' => 'medium',
                'progress_percentage' => 100,
                'project_manager_id' => 1,
                'created_by' => 1,
            ],
        ];

        foreach ($projects as $projectData) {
            $project = Project::create($projectData);
            
            // Log project creation activity
            Activity::logProject('created', $project, "تم إنشاء مشروع جديد: {$project->name_ar}", "New project created: {$project->name_en}");
            
            if ($project->status === 'completed') {
                Activity::logProject('completed', $project, "تم إكمال المشروع: {$project->name_ar}", "Project completed: {$project->name_en}");
            }
        }

        // Create sample tasks
        $tasks = [
            [
                'project_id' => 1,
                'title_ar' => 'تصميم واجهة المستخدم',
                'title_en' => 'User Interface Design',
                'description_ar' => 'تصميم واجهة المستخدم للموقع الإلكتروني',
                'status' => 'completed',
                'priority' => 'high',
                'progress_percentage' => 100,
                'assigned_to' => 1,
                'created_by' => 1,
                'due_date' => now()->subDays(5),
                'completed_date' => now()->subDays(3),
            ],
            [
                'project_id' => 1,
                'title_ar' => 'تطوير الواجهة الخلفية',
                'title_en' => 'Backend Development',
                'description_ar' => 'تطوير الواجهة الخلفية وقواعد البيانات',
                'status' => 'in_progress',
                'priority' => 'high',
                'progress_percentage' => 70,
                'assigned_to' => 1,
                'created_by' => 1,
                'due_date' => now()->addDays(10),
            ],
            [
                'project_id' => 2,
                'title_ar' => 'تطوير نظام الدفع',
                'title_en' => 'Payment System Development',
                'description_ar' => 'تطوير وتكامل نظام الدفع الإلكتروني',
                'status' => 'todo',
                'priority' => 'urgent',
                'progress_percentage' => 0,
                'assigned_to' => 1,
                'created_by' => 1,
                'due_date' => now()->addDays(5),
            ],
        ];

        foreach ($tasks as $taskData) {
            $task = ProjectTask::create($taskData);
            
            // Log task creation activity
            Activity::logTask('created', $task, "تم إنشاء مهمة جديدة: {$task->title_ar}", "New task created: {$task->title_en}");
            
            if ($task->status === 'completed') {
                Activity::logTask('completed', $task, "تم إكمال المهمة: {$task->title_ar}", "Task completed: {$task->title_en}");
            }
        }

        // Create sample invoices
        $invoices = [
            [
                'client_id' => 1,
                'project_id' => 1,
                'subject_ar' => 'فاتورة تطوير الموقع الإلكتروني - الدفعة الأولى',
                'subject_en' => 'Website Development Invoice - First Payment',
                'issue_date' => now()->subDays(20),
                'due_date' => now()->subDays(5),
                'subtotal' => 25000,
                'tax_rate' => 15,
                'tax_amount' => 3750,
                'total_amount' => 28750,
                'balance_due' => 0,
                'status' => 'paid',
                'payment_status' => 'paid',
                'paid_at' => now()->subDays(3),
                'created_by' => 1,
            ],
            [
                'client_id' => 2,
                'project_id' => 2,
                'subject_ar' => 'فاتورة تطوير التطبيق الجوال',
                'subject_en' => 'Mobile App Development Invoice',
                'issue_date' => now()->subDays(10),
                'due_date' => now()->addDays(20),
                'subtotal' => 37500,
                'tax_rate' => 15,
                'tax_amount' => 5625,
                'total_amount' => 43125,
                'balance_due' => 43125,
                'status' => 'sent',
                'payment_status' => 'unpaid',
                'sent_at' => now()->subDays(8),
                'created_by' => 1,
            ],
            [
                'client_id' => 3,
                'project_id' => 3,
                'subject_ar' => 'فاتورة نظام إدارة المخزون',
                'subject_en' => 'Inventory Management System Invoice',
                'issue_date' => now()->subDays(15),
                'due_date' => now()->subDays(1),
                'subtotal' => 40000,
                'tax_rate' => 15,
                'tax_amount' => 6000,
                'total_amount' => 46000,
                'balance_due' => 46000,
                'status' => 'sent',
                'payment_status' => 'overdue',
                'sent_at' => now()->subDays(13),
                'created_by' => 1,
            ],
        ];

        foreach ($invoices as $invoiceData) {
            $invoice = Invoice::create($invoiceData);
            
            // Log invoice creation activity
            Activity::logInvoice('created', $invoice, "تم إنشاء فاتورة جديدة: {$invoice->invoice_number}", "New invoice created: {$invoice->invoice_number}");
            
            if ($invoice->status === 'sent') {
                Activity::logInvoice('sent', $invoice, "تم إرسال الفاتورة: {$invoice->invoice_number}", "Invoice sent: {$invoice->invoice_number}");
            }
            
            if ($invoice->payment_status === 'paid') {
                Activity::logInvoice('paid', $invoice, "تم دفع الفاتورة: {$invoice->invoice_number}", "Invoice paid: {$invoice->invoice_number}");
            }
        }

        // Add some recent activities
        $recentActivities = [
            ['user', 'login', 'تم تسجيل الدخول إلى النظام', 'User logged into the system'],
            ['client', 'updated', 'تم تحديث بيانات العميل: شركة التقنية المتقدمة', 'Client updated: Advanced Technology Company'],
            ['project', 'updated', 'تم تحديث حالة المشروع: تطوير موقع إلكتروني متقدم', 'Project updated: Advanced Website Development'],
        ];

        foreach ($recentActivities as $activityData) {
            Activity::log(
                $activityData[0],
                $activityData[1],
                $activityData[2],
                $activityData[3],
                null,
                [],
                1
            );
        }
    }
}
