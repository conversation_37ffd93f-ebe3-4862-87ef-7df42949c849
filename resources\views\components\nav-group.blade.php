@props(['label', 'icon' => null, 'active' => false])

<div x-data="{ open: {{ $active ? 'true' : 'false' }} }" class="space-y-1">
    <button @click="open = !open" 
            class="w-full text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900 group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 text-right">
        @if($icon)
            <x-icon name="{{ $icon }}" class="ml-3 flex-shrink-0 h-5 w-5 text-secondary-400 group-hover:text-secondary-500" />
        @endif
        <span class="flex-1 font-arabic">{{ $label }}</span>
        <svg class="mr-2 h-4 w-4 transform transition-transform duration-200" 
             :class="{ 'rotate-90': open }" 
             fill="none" 
             stroke="currentColor" 
             viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </button>
    
    <div x-show="open" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="space-y-1 mr-6">
        {{ $slot }}
    </div>
</div>
