<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the role-based dashboard.
     */
    public function index(Request $request): View
    {
        $user = auth()->user();

        // Redirect based on user role
        if ($user->hasRole('founder')) {
            return $this->founderDashboard();
        } elseif ($user->hasRole('admin')) {
            return $this->adminDashboard();
        } elseif ($user->hasRole('manager')) {
            return $this->managerDashboard();
        } elseif ($user->hasRole('employee')) {
            return $this->employeeDashboard();
        } elseif ($user->hasRole('client')) {
            return $this->clientDashboard();
        }

        // Default dashboard for users without specific roles
        return view('dashboard', [
            'user' => $user,
            'dashboardType' => 'default'
        ]);
    }

    /**
     * Founder dashboard with complete system overview.
     */
    private function founderDashboard(): View
    {
        // Get comprehensive statistics for founder
        $stats = [
            'total_clients' => \App\Models\Client::count(),
            'active_projects' => \App\Models\Project::where('status', 'active')->count(),
            'monthly_revenue' => \App\Models\Invoice::whereMonth('created_at', now()->month)
                                                  ->where('payment_status', 'paid')
                                                  ->sum('total_amount'),
            'team_members' => \App\Models\User::whereHas('roles', function($q) {
                $q->whereIn('name', ['admin', 'manager', 'employee']);
            })->count(),
            'pending_invoices' => \App\Models\Invoice::where('payment_status', 'unpaid')->count(),
            'overdue_invoices' => \App\Models\Invoice::where('payment_status', 'overdue')->count(),
        ];

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'founder',
            'stats' => $stats
        ]);
    }

    /**
     * Admin dashboard with administrative overview.
     */
    private function adminDashboard(): View
    {
        $stats = [
            'total_clients' => \App\Models\Client::count(),
            'active_projects' => \App\Models\Project::where('status', 'active')->count(),
            'monthly_revenue' => \App\Models\Invoice::whereMonth('created_at', now()->month)
                                                  ->where('payment_status', 'paid')
                                                  ->sum('total_amount'),
            'team_members' => \App\Models\User::whereHas('roles', function($q) {
                $q->whereIn('name', ['manager', 'employee']);
            })->count(),
        ];

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'admin',
            'stats' => $stats
        ]);
    }

    /**
     * Manager dashboard with department-specific data.
     */
    private function managerDashboard(): View
    {
        $stats = [
            'assigned_projects' => \App\Models\Project::where('project_manager_id', auth()->id())->count(),
            'team_projects' => \App\Models\Project::where('status', 'active')->count(),
            'completed_tasks' => \App\Models\ProjectTask::where('assigned_to', auth()->id())
                                                       ->where('status', 'completed')
                                                       ->count(),
        ];

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'manager',
            'stats' => $stats
        ]);
    }

    /**
     * Employee dashboard with personal tasks and projects.
     */
    private function employeeDashboard(): View
    {
        $stats = [
            'assigned_tasks' => \App\Models\ProjectTask::where('assigned_to', auth()->id())
                                                      ->whereIn('status', ['todo', 'in_progress'])
                                                      ->count(),
            'completed_tasks' => \App\Models\ProjectTask::where('assigned_to', auth()->id())
                                                       ->where('status', 'completed')
                                                       ->count(),
            'active_projects' => \App\Models\Project::whereJsonContains('team_members', auth()->id())
                                                   ->where('status', 'active')
                                                   ->count(),
        ];

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'employee',
            'stats' => $stats
        ]);
    }

    /**
     * Client dashboard with project status and invoices.
     */
    private function clientDashboard(): View
    {
        $client = \App\Models\Client::where('email', auth()->user()->email)->first();

        $stats = [];
        if ($client) {
            $stats = [
                'active_projects' => $client->projects()->where('status', 'active')->count(),
                'completed_projects' => $client->projects()->where('status', 'completed')->count(),
                'pending_invoices' => $client->invoices()->where('payment_status', 'unpaid')->count(),
                'total_spent' => $client->invoices()->where('payment_status', 'paid')->sum('total_amount'),
            ];
        }

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'client',
            'stats' => $stats,
            'client' => $client
        ]);
    }
}
