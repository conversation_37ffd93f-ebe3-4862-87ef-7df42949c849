<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the role-based dashboard.
     */
    public function index(Request $request): View
    {
        $user = auth()->user();

        // Redirect based on user role
        if ($user->hasRole('founder')) {
            return $this->founderDashboard();
        } elseif ($user->hasRole('admin')) {
            return $this->adminDashboard();
        } elseif ($user->hasRole('manager')) {
            return $this->managerDashboard();
        } elseif ($user->hasRole('employee')) {
            return $this->employeeDashboard();
        } elseif ($user->hasRole('client')) {
            return $this->clientDashboard();
        }

        // Default dashboard for users without specific roles
        return view('dashboard', [
            'user' => $user,
            'dashboardType' => 'default'
        ]);
    }

    /**
     * Founder dashboard with complete system overview.
     */
    private function founderDashboard(): View
    {
        // Get comprehensive statistics for founder
        $stats = $this->getFounderStats();
        $chartData = $this->getFounderChartData();
        $recentActivities = $this->getRecentActivities();
        $kpis = $this->getFounderKPIs();

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'founder',
            'stats' => $stats,
            'chartData' => $chartData,
            'recentActivities' => $recentActivities,
            'kpis' => $kpis
        ]);
    }

    /**
     * Get comprehensive founder statistics.
     */
    private function getFounderStats(): array
    {
        return [
            'total_clients' => \App\Models\Client::count(),
            'active_clients' => \App\Models\Client::where('status', 'active')->count(),
            'total_projects' => \App\Models\Project::count(),
            'active_projects' => \App\Models\Project::where('status', 'active')->count(),
            'completed_projects' => \App\Models\Project::where('status', 'completed')->count(),
            'monthly_revenue' => \App\Models\Invoice::whereMonth('created_at', now()->month)
                                                  ->where('payment_status', 'paid')
                                                  ->sum('total_amount'),
            'yearly_revenue' => \App\Models\Invoice::whereYear('created_at', now()->year)
                                                 ->where('payment_status', 'paid')
                                                 ->sum('total_amount'),
            'team_members' => \App\Models\User::whereHas('roles', function($q) {
                $q->whereIn('name', ['admin', 'manager', 'employee']);
            })->count(),
            'pending_invoices' => \App\Models\Invoice::where('payment_status', 'unpaid')->count(),
            'overdue_invoices' => \App\Models\Invoice::where('payment_status', 'overdue')->count(),
            'total_invoices' => \App\Models\Invoice::count(),
            'pending_tasks' => \App\Models\ProjectTask::whereIn('status', ['todo', 'in_progress'])->count(),
            'completed_tasks' => \App\Models\ProjectTask::where('status', 'completed')->count(),
            'overdue_tasks' => \App\Models\ProjectTask::where('due_date', '<', now())
                                                    ->whereNotIn('status', ['completed', 'cancelled'])
                                                    ->count(),
        ];
    }

    /**
     * Admin dashboard with administrative overview.
     */
    private function adminDashboard(): View
    {
        $stats = [
            'total_clients' => \App\Models\Client::count(),
            'active_projects' => \App\Models\Project::where('status', 'active')->count(),
            'monthly_revenue' => \App\Models\Invoice::whereMonth('created_at', now()->month)
                                                  ->where('payment_status', 'paid')
                                                  ->sum('total_amount'),
            'team_members' => \App\Models\User::whereHas('roles', function($q) {
                $q->whereIn('name', ['manager', 'employee']);
            })->count(),
        ];

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'admin',
            'stats' => $stats
        ]);
    }

    /**
     * Manager dashboard with department-specific data.
     */
    private function managerDashboard(): View
    {
        $stats = [
            'assigned_projects' => \App\Models\Project::where('project_manager_id', auth()->id())->count(),
            'team_projects' => \App\Models\Project::where('status', 'active')->count(),
            'completed_tasks' => \App\Models\ProjectTask::where('assigned_to', auth()->id())
                                                       ->where('status', 'completed')
                                                       ->count(),
        ];

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'manager',
            'stats' => $stats
        ]);
    }

    /**
     * Employee dashboard with personal tasks and projects.
     */
    private function employeeDashboard(): View
    {
        $stats = [
            'assigned_tasks' => \App\Models\ProjectTask::where('assigned_to', auth()->id())
                                                      ->whereIn('status', ['todo', 'in_progress'])
                                                      ->count(),
            'completed_tasks' => \App\Models\ProjectTask::where('assigned_to', auth()->id())
                                                       ->where('status', 'completed')
                                                       ->count(),
            'active_projects' => \App\Models\Project::whereJsonContains('team_members', auth()->id())
                                                   ->where('status', 'active')
                                                   ->count(),
        ];

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'employee',
            'stats' => $stats
        ]);
    }

    /**
     * Client dashboard with project status and invoices.
     */
    private function clientDashboard(): View
    {
        $client = \App\Models\Client::where('email', auth()->user()->email)->first();

        $stats = [];
        if ($client) {
            $stats = [
                'active_projects' => $client->projects()->where('status', 'active')->count(),
                'completed_projects' => $client->projects()->where('status', 'completed')->count(),
                'pending_invoices' => $client->invoices()->where('payment_status', 'unpaid')->count(),
                'total_spent' => $client->invoices()->where('payment_status', 'paid')->sum('total_amount'),
            ];
        }

        return view('dashboard', [
            'user' => auth()->user(),
            'dashboardType' => 'client',
            'stats' => $stats,
            'client' => $client
        ]);
    }

    /**
     * Get chart data for founder dashboard.
     */
    private function getFounderChartData(): array
    {
        return [
            'revenue_trend' => $this->getRevenueTrendData(),
            'project_status' => $this->getProjectStatusData(),
            'client_acquisition' => $this->getClientAcquisitionData(),
            'team_productivity' => $this->getTeamProductivityData(),
        ];
    }

    /**
     * Get revenue trend data for the last 12 months.
     */
    private function getRevenueTrendData(): array
    {
        $months = [];
        $revenues = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = $date->format('M Y');

            $revenue = \App\Models\Invoice::whereYear('created_at', $date->year)
                                        ->whereMonth('created_at', $date->month)
                                        ->where('payment_status', 'paid')
                                        ->sum('total_amount');
            $revenues[] = (float) $revenue;
        }

        return [
            'labels' => $months,
            'data' => $revenues,
        ];
    }

    /**
     * Get project status distribution data.
     */
    private function getProjectStatusData(): array
    {
        $statuses = ['planning', 'active', 'on_hold', 'completed', 'cancelled'];
        $data = [];
        $labels = [];

        foreach ($statuses as $status) {
            $count = \App\Models\Project::where('status', $status)->count();
            if ($count > 0) {
                $data[] = $count;
                $labels[] = match($status) {
                    'planning' => 'التخطيط',
                    'active' => 'نشط',
                    'on_hold' => 'معلق',
                    'completed' => 'مكتمل',
                    'cancelled' => 'ملغي',
                    default => $status
                };
            }
        }

        return [
            'labels' => $labels,
            'data' => $data,
        ];
    }

    /**
     * Get client acquisition data for the last 6 months.
     */
    private function getClientAcquisitionData(): array
    {
        $months = [];
        $clients = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = $date->format('M');

            $count = \App\Models\Client::whereYear('created_at', $date->year)
                                     ->whereMonth('created_at', $date->month)
                                     ->count();
            $clients[] = $count;
        }

        return [
            'labels' => $months,
            'data' => $clients,
        ];
    }

    /**
     * Get team productivity data.
     */
    private function getTeamProductivityData(): array
    {
        $users = \App\Models\User::whereHas('roles', function($q) {
            $q->whereIn('name', ['admin', 'manager', 'employee']);
        })->with('roles')->get();

        $data = [];
        foreach ($users as $user) {
            $completedTasks = \App\Models\ProjectTask::where('assigned_to', $user->id)
                                                   ->where('status', 'completed')
                                                   ->whereMonth('created_at', now()->month)
                                                   ->count();

            if ($completedTasks > 0) {
                $data[] = [
                    'name' => $user->name,
                    'tasks' => $completedTasks,
                ];
            }
        }

        return $data;
    }

    /**
     * Get recent activities for dashboard.
     */
    private function getRecentActivities(): array
    {
        return \App\Models\Activity::getDashboardSummary(15);
    }

    /**
     * Get founder KPIs.
     */
    private function getFounderKPIs(): array
    {
        $currentMonth = now();
        $lastMonth = now()->subMonth();

        // Current month stats
        $currentMonthRevenue = \App\Models\Invoice::whereMonth('created_at', $currentMonth->month)
                                                 ->whereYear('created_at', $currentMonth->year)
                                                 ->where('payment_status', 'paid')
                                                 ->sum('total_amount');

        $lastMonthRevenue = \App\Models\Invoice::whereMonth('created_at', $lastMonth->month)
                                              ->whereYear('created_at', $lastMonth->year)
                                              ->where('payment_status', 'paid')
                                              ->sum('total_amount');

        $revenueGrowth = $lastMonthRevenue > 0
            ? (($currentMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100
            : 0;

        // Project completion rate
        $totalProjects = \App\Models\Project::count();
        $completedProjects = \App\Models\Project::where('status', 'completed')->count();
        $projectCompletionRate = $totalProjects > 0 ? ($completedProjects / $totalProjects) * 100 : 0;

        // Client satisfaction (placeholder - would need actual feedback system)
        $clientSatisfaction = 85; // This would come from actual client feedback

        // Team efficiency
        $totalTasks = \App\Models\ProjectTask::count();
        $completedTasks = \App\Models\ProjectTask::where('status', 'completed')->count();
        $teamEfficiency = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;

        return [
            'revenue_growth' => round($revenueGrowth, 1),
            'project_completion_rate' => round($projectCompletionRate, 1),
            'client_satisfaction' => $clientSatisfaction,
            'team_efficiency' => round($teamEfficiency, 1),
        ];
    }

    /**
     * API endpoint to get dashboard stats.
     */
    public function getStats()
    {
        $user = auth()->user();

        if ($user->hasRole('founder')) {
            return response()->json($this->getFounderStats());
        }

        return response()->json([]);
    }

    /**
     * API endpoint to get recent activities.
     */
    public function getActivities()
    {
        return response()->json($this->getRecentActivities());
    }

    /**
     * API endpoint to get chart data.
     */
    public function getChartData()
    {
        $user = auth()->user();

        if ($user->hasRole('founder')) {
            return response()->json($this->getFounderChartData());
        }

        return response()->json([]);
    }
}
