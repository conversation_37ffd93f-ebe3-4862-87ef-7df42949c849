<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clients Table
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Public identifier');
            $table->string('client_code')->unique()->comment('Internal client code');
            $table->enum('type', ['individual', 'company'])->default('individual');

            // Personal Information
            $table->string('name_ar')->comment('Arabic name');
            $table->string('name_en')->nullable()->comment('English name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('whatsapp')->nullable();
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('nationality')->nullable();
            $table->string('id_number')->nullable()->comment('National ID or Passport');

            // Company Information (if type is company)
            $table->string('company_name_ar')->nullable();
            $table->string('company_name_en')->nullable();
            $table->string('commercial_register')->nullable();
            $table->string('tax_number')->nullable();
            $table->string('industry')->nullable();
            $table->integer('company_size')->nullable()->comment('Number of employees');
            $table->string('website')->nullable();

            // Address Information
            $table->string('country')->default('Saudi Arabia');
            $table->string('city')->nullable();
            $table->string('district')->nullable();
            $table->text('address')->nullable();
            $table->string('postal_code')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();

            // Business Information
            $table->enum('status', ['active', 'inactive', 'suspended', 'blacklisted'])->default('active');
            $table->enum('priority', ['low', 'medium', 'high', 'vip'])->default('medium');
            $table->enum('source', ['website', 'referral', 'social_media', 'advertising', 'cold_call', 'other'])->default('website');
            $table->string('referral_source')->nullable();
            $table->decimal('credit_limit', 10, 2)->default(0);
            $table->integer('payment_terms')->default(30)->comment('Payment terms in days');
            $table->text('notes')->nullable();
            $table->json('tags')->nullable();
            $table->json('custom_fields')->nullable();

            // Relationship Management
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('last_contact_at')->nullable();
            $table->timestamp('next_follow_up_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->index(['status', 'priority']);
            $table->index(['type', 'status']);
            $table->index('assigned_to');
            $table->index('source');
            $table->index('last_contact_at');
            $table->index('next_follow_up_at');
            $table->fullText(['name_ar', 'company_name_ar', 'email']);
        });

        // Client Contacts Table (for additional contacts within a company)
        Schema::create('client_contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->string('position_ar')->nullable();
            $table->string('position_en')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('whatsapp')->nullable();
            $table->enum('type', ['primary', 'secondary', 'technical', 'financial', 'decision_maker'])->default('secondary');
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'is_active']);
            $table->index('type');
        });

        // Communication History Table
        Schema::create('client_communications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->foreignId('contact_id')->nullable()->constrained('client_contacts')->onDelete('set null');
            $table->enum('type', ['call', 'email', 'meeting', 'whatsapp', 'sms', 'visit', 'other'])->default('call');
            $table->enum('direction', ['inbound', 'outbound'])->default('outbound');
            $table->string('subject_ar');
            $table->string('subject_en')->nullable();
            $table->text('content_ar');
            $table->text('content_en')->nullable();
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'no_response'])->default('completed');
            $table->datetime('scheduled_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->integer('duration_minutes')->nullable();
            $table->enum('outcome', ['positive', 'neutral', 'negative', 'follow_up_required'])->nullable();
            $table->text('outcome_notes')->nullable();
            $table->datetime('next_follow_up_at')->nullable();
            $table->json('attachments')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'type']);
            $table->index(['scheduled_at', 'status']);
            $table->index('next_follow_up_at');
            $table->index('outcome');
        });

        // Client Documents Table
        Schema::create('client_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->enum('type', ['contract', 'proposal', 'invoice', 'receipt', 'id_copy', 'commercial_register', 'tax_certificate', 'other'])->default('other');
            $table->string('file_path');
            $table->string('file_name');
            $table->string('file_type');
            $table->integer('file_size');
            $table->boolean('is_confidential')->default(false);
            $table->date('expiry_date')->nullable();
            $table->json('access_permissions')->nullable();
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'type']);
            $table->index('expiry_date');
            $table->index('is_confidential');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_documents');
        Schema::dropIfExists('client_communications');
        Schema::dropIfExists('client_contacts');
        Schema::dropIfExists('clients');
    }
};
